import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { TypeAgentTools } from '../entities/type-agent-tools.entity';

/**
 * Repository cho TypeAgentTools entity
 */
@Injectable()
export class TypeAgentToolsRepository extends Repository<TypeAgentTools> {
  constructor(private dataSource: DataSource) {
    super(TypeAgentTools, dataSource.createEntityManager());
  }

  /**
   * Lấy danh sách tool IDs theo type agent ID
   * @param typeAgentId ID của type agent
   * @returns Danh sách tool IDs
   */
  async getToolIdsByTypeAgent(typeAgentId: number): Promise<string[]> {
    const result = await this.createQueryBuilder('tat')
      .select('tat.tool_id', 'toolId')
      .where('tat.type_agent_id = :typeAgentId', { typeAgentId })
      .getRawMany();

    return result.map(row => row.toolId);
  }

  /**
   * <PERSON><PERSON>y danh sách type agent IDs theo tool ID
   * @param toolId ID của tool
   * @returns Danh sách type agent IDs
   */
  async getTypeAgentIdsByTool(toolId: string): Promise<number[]> {
    const result = await this.createQueryBuilder('tat')
      .select('tat.type_agent_id', 'typeAgentId')
      .where('tat.tool_id = :toolId', { toolId })
      .getRawMany();

    return result.map(row => row.typeAgentId);
  }

  /**
   * Thêm tool vào type agent
   * @param typeAgentId ID của type agent
   * @param toolId ID của tool
   */
  async addToolToTypeAgent(typeAgentId: number, toolId: string): Promise<void> {
    await this.createQueryBuilder()
      .insert()
      .into(TypeAgentTools)
      .values({ typeAgentId, toolId })
      .orIgnore()
      .execute();
  }

  /**
   * Xóa tool khỏi type agent
   * @param typeAgentId ID của type agent
   * @param toolId ID của tool
   */
  async removeToolFromTypeAgent(typeAgentId: number, toolId: string): Promise<void> {
    await this.createQueryBuilder()
      .delete()
      .from(TypeAgentTools)
      .where('type_agent_id = :typeAgentId AND tool_id = :toolId', { typeAgentId, toolId })
      .execute();
  }

  /**
   * Xóa tất cả tools của type agent
   * @param typeAgentId ID của type agent
   */
  async removeAllToolsFromTypeAgent(typeAgentId: number): Promise<void> {
    await this.createQueryBuilder()
      .delete()
      .from(TypeAgentTools)
      .where('type_agent_id = :typeAgentId', { typeAgentId })
      .execute();
  }

  /**
   * Kiểm tra xem type agent có tool hay không
   * @param typeAgentId ID của type agent
   * @param toolId ID của tool
   * @returns true nếu có, false nếu không
   */
  async hasToolInTypeAgent(typeAgentId: number, toolId: string): Promise<boolean> {
    const count = await this.createQueryBuilder('tat')
      .where('tat.type_agent_id = :typeAgentId AND tat.tool_id = :toolId', { typeAgentId, toolId })
      .getCount();

    return count > 0;
  }
}
