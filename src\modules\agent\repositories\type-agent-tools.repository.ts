import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { TypeAgentTools } from '../entities/type-agent-tools.entity';
import { PaginatedResult } from '@common/response';

/**
 * Repository cho TypeAgentTools entity
 */
@Injectable()
export class TypeAgentToolsRepository extends Repository<TypeAgentTools> {
  constructor(private dataSource: DataSource) {
    super(TypeAgentTools, dataSource.createEntityManager());
  }

  /**
   * L<PERSON>y danh sách tool IDs theo type agent ID
   * @param typeAgentId ID của type agent
   * @returns Danh sách tool IDs
   */
  async getToolIdsByTypeAgent(typeAgentId: number): Promise<string[]> {
    const result = await this.createQueryBuilder('tat')
      .select('tat.tool_id', 'toolId')
      .where('tat.type_agent_id = :typeAgentId', { typeAgentId })
      .getRawMany();

    return result.map(row => row.toolId);
  }

  /**
   * <PERSON><PERSON><PERSON> danh sách type agent IDs theo tool ID
   * @param toolId ID của tool
   * @returns Danh sách type agent IDs
   */
  async getTypeAgentIdsByTool(toolId: string): Promise<number[]> {
    const result = await this.createQueryBuilder('tat')
      .select('tat.type_agent_id', 'typeAgentId')
      .where('tat.tool_id = :toolId', { toolId })
      .getRawMany();

    return result.map(row => row.typeAgentId);
  }

  /**
   * Thêm tool vào type agent
   * @param typeAgentId ID của type agent
   * @param toolId ID của tool
   */
  async addToolToTypeAgent(typeAgentId: number, toolId: string): Promise<void> {
    await this.createQueryBuilder()
      .insert()
      .into(TypeAgentTools)
      .values({ typeAgentId, toolId })
      .orIgnore()
      .execute();
  }

  /**
   * Xóa tool khỏi type agent
   * @param typeAgentId ID của type agent
   * @param toolId ID của tool
   */
  async removeToolFromTypeAgent(typeAgentId: number, toolId: string): Promise<void> {
    await this.createQueryBuilder()
      .delete()
      .from(TypeAgentTools)
      .where('type_agent_id = :typeAgentId AND tool_id = :toolId', { typeAgentId, toolId })
      .execute();
  }

  /**
   * Xóa tất cả tools của type agent
   * @param typeAgentId ID của type agent
   */
  async removeAllToolsFromTypeAgent(typeAgentId: number): Promise<void> {
    await this.createQueryBuilder()
      .delete()
      .from(TypeAgentTools)
      .where('type_agent_id = :typeAgentId', { typeAgentId })
      .execute();
  }

  /**
   * Kiểm tra xem type agent có tool hay không
   * @param typeAgentId ID của type agent
   * @param toolId ID của tool
   * @returns true nếu có, false nếu không
   */
  async hasToolInTypeAgent(typeAgentId: number, toolId: string): Promise<boolean> {
    const count = await this.createQueryBuilder('tat')
      .where('tat.type_agent_id = :typeAgentId AND tat.tool_id = :toolId', { typeAgentId, toolId })
      .getCount();

    return count > 0;
  }

  /**
   * Lấy danh sách tools với thông tin chi tiết và phân trang
   * @param typeAgentId ID của type agent
   * @param page Số trang
   * @param limit Số lượng items per page
   * @param search Từ khóa tìm kiếm
   * @returns Danh sách tools với phân trang
   */
  async getToolsWithDetailsByTypeAgent(
    typeAgentId: number,
    page: number = 1,
    limit: number = 10,
    search?: string
  ): Promise<PaginatedResult<any>> {
    const queryBuilder = this.createQueryBuilder('tat')
      .leftJoin('admin_tools', 'tool', 'tool.id = tat.tool_id')
      .select([
        'tool.id AS id',
        'tool.name AS name',
        'tool.description AS description',
        'tool.status AS status',
        'EXTRACT(EPOCH FROM tool.created_at) * 1000 AS "createdAt"'
      ])
      .where('tat.type_agent_id = :typeAgentId', { typeAgentId });

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      queryBuilder.andWhere('tool.name ILIKE :search', { search: `%${search}%` });
    }

    // Đếm tổng số records
    const totalItems = await queryBuilder.getCount();

    // Thêm phân trang
    const offset = (page - 1) * limit;
    queryBuilder
      .orderBy('tool.created_at', 'DESC')
      .limit(limit)
      .offset(offset);

    const items = await queryBuilder.getRawMany();

    return {
      items,
      meta: {
        currentPage: page,
        itemsPerPage: limit,
        totalItems,
        totalPages: Math.ceil(totalItems / limit),
        itemCount: items.length,
      },
    };
  }
}
