import { Injectable, Logger } from '@nestjs/common';
import { TypeAgentRepository } from '@modules/agent/repositories/type-agent.repository';
import { AgentUserRepository } from '@modules/agent/repositories/agent-user.repository';
import { AgentSystemRepository } from '@modules/agent/repositories/agent-system.repository';
import { TypeAgentAgentSystemRepository } from '@modules/agent/repositories/type-agent-agent-system.repository';
import { TypeAgentToolsRepository } from '@modules/agent/repositories/type-agent-tools.repository';
import { TypeAgentModelsRepository } from '@modules/agent/repositories/type-agent-models.repository';
import {
  CreateTypeAgentDto,
  TypeAgentDetailDto,
  TypeAgentListItemDto,
  TypeAgentQueryDto,
  TypeAgentTrashItemDto,
  UpdateTypeAgentDto,
  UpdateTypeAgentStatusDto,
  AddAgentSystemsDto,
  RemoveAgentSystemsDto,
  TypeAgentSystemsQueryDto,
  TypeAgentSystemItemDto,
  TypeAgentSystemSortBy,
} from '../dto/type-agent';
import { TypeAgent } from '@modules/agent/entities';
import { TypeAgentStatus } from '@modules/agent/constants';
import { PaginatedResult } from '@/common/response';
import { AppException } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions/agent-error.code';
import { Transactional } from 'typeorm-transactional';

import { EmployeeInfoService } from '@modules/employee/services/employee-info.service';
import { EmployeeInfoDto } from '@modules/agent/admin/dto/common';
import { EmployeeInfoSimpleDto } from '@/modules/employee/dto/employee-info-simple.dto';
import { CdnService } from '@shared/services/cdn.service';
import { TimeIntervalEnum } from '@utils/time';

/**
 * Service xử lý logic nghiệp vụ cho Type Agent
 */
@Injectable()
export class AdminTypeAgentService {
  private readonly logger = new Logger(AdminTypeAgentService.name);

  constructor(
    private readonly typeAgentRepository: TypeAgentRepository,
    private readonly agentUserRepository: AgentUserRepository,
    private readonly agentSystemRepository: AgentSystemRepository,
    private readonly typeAgentAgentSystemRepository: TypeAgentAgentSystemRepository,
    private readonly typeAgentToolsRepository: TypeAgentToolsRepository,
    private readonly typeAgentModelsRepository: TypeAgentModelsRepository,
    private readonly employeeInfoService: EmployeeInfoService,
    private readonly cdnService: CdnService,
  ) {}

  /**
   * Lấy danh sách loại agent với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách loại agent với phân trang
   */
  async findAll(
    queryDto: TypeAgentQueryDto,
  ): Promise<PaginatedResult<TypeAgentListItemDto>> {
    try {
      const { page, limit, search, sortBy, sortDirection } = queryDto;

      // Lấy danh sách loại agent từ repository (không filter theo status)
      const result = await this.typeAgentRepository.findPaginated(
        page,
        limit,
        search,
        undefined, // Không filter theo status
        undefined,
        sortBy,
        sortDirection,
      );

      // Chuyển đổi kết quả sang DTO với tools và models
      const items = await Promise.all(
        result.items.map(async (typeAgent) => {
          // Lấy tools và models cho từng type agent
          const [toolIds, modelIds] = await Promise.all([
            this.typeAgentToolsRepository.getToolIdsByTypeAgent(typeAgent.id),
            this.typeAgentModelsRepository.getModelRegistryIdsByTypeAgent(typeAgent.id)
          ]);

          return this.mapToListItemDto(typeAgent, toolIds, modelIds);
        })
      );

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(
        `Error finding type agents: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_QUERY_FAILED);
    }
  }

  /**
   * Lấy thông tin chi tiết loại agent theo ID
   * @param id ID của loại agent
   * @returns Thông tin chi tiết loại agent
   */
  async findById(id: number): Promise<TypeAgentDetailDto> {
    // Lấy thông tin loại agent từ repository
    const typeAgent = await this.typeAgentRepository.findById(id);

    if (!typeAgent) {
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
    }

    try {
      // Lấy danh sách agent systems, tools và models
      this.logger.log(`🔍 SERVICE DEBUG: About to call findAgentSystemsByTypeAgentId for id: ${id}`);
      const [agentSystems, toolIds, modelIds] = await Promise.all([
        this.typeAgentRepository.findAgentSystemsByTypeAgentId(id),
        this.typeAgentToolsRepository.getToolIdsByTypeAgent(id),
        this.typeAgentModelsRepository.getModelRegistryIdsByTypeAgent(id)
      ]);
      this.logger.log(`🔍 SERVICE DEBUG: Received agentSystems:`, agentSystems);

      // Lấy thông tin employee
      const employeeInfo = await this.getEmployeeInfoForTypeAgent(typeAgent);

      return await this.mapToDetailDto(typeAgent, agentSystems, employeeInfo, toolIds, modelIds);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error finding type agent by id: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_FETCH_FAILED);
    }
  }

  /**
   * Tạo loại agent mới
   * @param createDto Dữ liệu tạo loại agent
   * @param employeeId ID của nhân viên tạo
   * @returns ID của loại agent đã tạo
   */
  @Transactional()
  async create(
    createDto: CreateTypeAgentDto,
    employeeId: number,
  ): Promise<{id: number}> {
    // Kiểm tra tên loại agent đã tồn tại chưa
    const existingTypeAgent = await this.typeAgentRepository.findByName(
      createDto.name,
    );
    if (existingTypeAgent) {
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NAME_EXISTS);
    }

    // Kiểm tra agent systems có tồn tại và không phải supervisor (nếu có)
    if (createDto.agentSystems && createDto.agentSystems.length > 0) {
      // Kiểm tra tồn tại
      const existingAgentSystemIds = await this.agentSystemRepository.findExistingIds(
        createDto.agentSystems,
      );
      if (existingAgentSystemIds.length !== createDto.agentSystems.length) {
        const missingIds = createDto.agentSystems.filter(id => !existingAgentSystemIds.includes(id));
        this.logger.warn(`Agent systems not found: ${missingIds.join(', ')}`);
        throw new AppException(AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND);
      }

      // Kiểm tra không được chọn supervisor agents
      const supervisorIds = await this.agentSystemRepository.findSupervisorIds(
        createDto.agentSystems,
      );
      if (supervisorIds.length > 0) {
        this.logger.warn(`Cannot assign supervisor agents to type agent: ${supervisorIds.join(', ')}`);
        throw new AppException(AGENT_ERROR_CODES.AGENT_SYSTEM_SUPERVISOR_NOT_ALLOWED);
      }
    }

    // Validate tools exist (nếu có)
    if (createDto.toolIds && createDto.toolIds.length > 0) {
      // Note: Có thể thêm validation tools tồn tại ở đây nếu cần
      // Hiện tại chỉ log để biết có tools được gán
      this.logger.log(`Creating type agent with ${createDto.toolIds.length} tools`);
    }

    // Validate models exist (nếu có)
    if (createDto.modelRegistryIds && createDto.modelRegistryIds.length > 0) {
      // Note: Có thể thêm validation models tồn tại ở đây nếu cần
      // Hiện tại chỉ log để biết có models được gán
      this.logger.log(`Creating type agent with ${createDto.modelRegistryIds.length} models`);
    }

    try {
      // Tạo loại agent mới
      const typeAgent = new TypeAgent();
      typeAgent.name = createDto.name;
      typeAgent.description = createDto.description;
      typeAgent.config = createDto.defaultConfig;
      typeAgent.status = createDto.status;
      typeAgent.createdBy = employeeId;
      typeAgent.updatedBy = employeeId;

      // Lưu loại agent
      const savedTypeAgent = await this.typeAgentRepository.save(typeAgent);

      // Liên kết với agent systems nếu có
      if (createDto.agentSystems && createDto.agentSystems.length > 0) {
        this.logger.log(`Linking type agent ${savedTypeAgent.id} with ${createDto.agentSystems.length} agent systems`);
        await this.typeAgentAgentSystemRepository.linkTypeWithAgentsReset(
          savedTypeAgent.id,
          createDto.agentSystems,
        );
      }

      // Liên kết với tools nếu có
      if (createDto.toolIds && createDto.toolIds.length > 0) {
        this.logger.log(`Linking type agent ${savedTypeAgent.id} with ${createDto.toolIds.length} tools`);
        for (const toolId of createDto.toolIds) {
          await this.typeAgentToolsRepository.addToolToTypeAgent(savedTypeAgent.id, toolId);
        }
      }

      // Liên kết với models nếu có
      if (createDto.modelRegistryIds && createDto.modelRegistryIds.length > 0) {
        this.logger.log(`Linking type agent ${savedTypeAgent.id} with ${createDto.modelRegistryIds.length} models`);
        await this.typeAgentModelsRepository.replaceModelsForTypeAgent(savedTypeAgent.id, createDto.modelRegistryIds);
      }

      this.logger.log(`Successfully created type agent with ID: ${savedTypeAgent.id}`);
      return {id: savedTypeAgent.id};
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error creating type agent: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_CREATION_FAILED);
    }
  }

  /**
   * Cập nhật thông tin loại agent
   * @param id ID của loại agent
   * @param updateDto Dữ liệu cập nhật
   * @param employeeId ID của nhân viên cập nhật
   */
  @Transactional()
  async update(
    id: number,
    updateDto: UpdateTypeAgentDto,
    employeeId: number,
  ): Promise<{id: number}> {
    // Kiểm tra loại agent có tồn tại không
    const typeAgent = await this.typeAgentRepository.findById(id);
    if (!typeAgent) {
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
    }

    // Kiểm tra xem loại agent đã bị xóa mềm hay chưa
    if (typeAgent.deletedAt) {
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_ALREADY_DELETED);
    }

    // Kiểm tra tên loại agent đã tồn tại chưa (nếu có cập nhật tên)
    if (updateDto.name && updateDto.name !== typeAgent.name) {
      const existingTypeAgent = await this.typeAgentRepository.findByName(
        updateDto.name,
      );
      if (existingTypeAgent && existingTypeAgent.id !== id) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NAME_EXISTS);
      }
    }

    try {
      // Cập nhật thông tin loại agent
      if (updateDto.name) typeAgent.name = updateDto.name;
      if (updateDto.description !== undefined)
        typeAgent.description = updateDto.description;
      if (updateDto.defaultConfig) typeAgent.config = updateDto.defaultConfig;
      if (updateDto.status) typeAgent.status = updateDto.status;
      typeAgent.updatedBy = employeeId;

      // Lưu loại agent
      await this.typeAgentRepository.update({id}, typeAgent);

      return {id};
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error updating type agent: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Cập nhật trạng thái loại agent
   * @param id ID của loại agent
   * @param updateStatusDto Dữ liệu cập nhật trạng thái
   * @param employeeId ID của nhân viên cập nhật
   */
  @Transactional()
  async updateStatus(
    id: number,
    updateStatusDto: UpdateTypeAgentStatusDto,
    employeeId: number,
  ): Promise<void> {
    // Kiểm tra loại agent có tồn tại không
    const typeAgent = await this.typeAgentRepository.findById(id);
    if (!typeAgent) {
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
    }

    // Kiểm tra xem loại agent đã bị xóa mềm hay chưa
    if (typeAgent.deletedAt) {
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_ALREADY_DELETED);
    }
    try {
      // Cập nhật trạng thái
      typeAgent.status = updateStatusDto.status;
      typeAgent.updatedBy = employeeId;

      // Lưu loại agent
      await this.typeAgentRepository.save(typeAgent);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error updating type agent status: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_STATUS_UPDATE_FAILED);
    }
  }

  /**
   * Xóa loại agent (soft delete)
   * @param id ID của loại agent
   * @param employeeId ID của nhân viên xóa
   */
  @Transactional()
  async remove(id: number, employeeId: number): Promise<void> {
    // Kiểm tra loại agent có tồn tại không
    const typeAgent = await this.typeAgentRepository.findById(id);
    if (!typeAgent) {
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
    }
    try {
      // Sử dụng phương thức xóa mềm tùy chỉnh thay vì softDelete của TypeORM
      const result = await this.typeAgentRepository.customSoftDelete(
        id,
        employeeId,
      );

      if (!result) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_DELETE_FAILED);
      }

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error removing type agent: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_DELETE_FAILED);
    }
  }





  /**
   * Cập nhật trạng thái type agent theo query param
   * @param id ID của type agent
   * @param draft true để chuyển về DRAFT, false để chuyển về APPROVED
   * @param employeeId ID của nhân viên cập nhật
   */
  async updateStatusByQuery(id: number, draft: boolean, employeeId: number): Promise<void> {
    try {
      // Kiểm tra type agent có tồn tại không
      const typeAgent = await this.typeAgentRepository.findById(id);
      if (!typeAgent) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }

      // Xác định status mới
      // Cập nhật status
      typeAgent.status = draft ? TypeAgentStatus.DRAFT : TypeAgentStatus.APPROVED;
      typeAgent.updatedBy = employeeId;

      await this.typeAgentRepository.save(typeAgent);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error updating type agent status: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_STATUS_UPDATE_FAILED);
    }
  }

  /**
   * Xóa type agent với migration agents sang type mới
   * @param id ID của type agent cần xóa
   * @param newTypeAgentId ID của type agent mới để migrate
   * @param employeeId ID của nhân viên xóa
   * @returns Số lượng agents đã được migrate
   */
  async removeWithMigration(id: number, newTypeAgentId: number, employeeId: number): Promise<number> {
    try {
      // Kiểm tra type agent cần xóa có tồn tại không
      const typeAgent = await this.typeAgentRepository.findById(id);
      if (!typeAgent) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }

      // Kiểm tra type agent mới có tồn tại không
      const newTypeAgent = await this.typeAgentRepository.findById(newTypeAgentId);
      if (!newTypeAgent) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }

      // Migrate agents sang type mới
      const migratedCount = await this.agentUserRepository.migrateAgents(id, newTypeAgentId);

      // Soft delete type agent
      await this.typeAgentRepository.customSoftDelete(id, employeeId);

      return migratedCount;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error removing type agent with migration: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_DELETE_FAILED);
    }
  }

  /**
   * Lấy danh sách type agents đã xóa mềm
   * @param queryDto Tham số truy vấn
   * @returns Danh sách type agents đã xóa với phân trang
   */
  async findAllDeleted(queryDto: TypeAgentQueryDto): Promise<PaginatedResult<TypeAgentTrashItemDto>> {
    try {
      const result = await this.typeAgentRepository.findDeletedPaginated(
        queryDto.page,
        queryDto.limit,
        queryDto.search,
        queryDto.sortBy,
        queryDto.sortDirection,
      );

      // Chuyển đổi kết quả sang DTO
      const items = await Promise.all(
        result.items.map((typeAgent) => this.mapToTrashItemDto(typeAgent))
      );

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Error fetching deleted type agents: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_QUERY_FAILED);
    }
  }

  /**
   * Khôi phục type agent đã xóa mềm (single)
   * @param id ID của type agent cần khôi phục
   * @param employeeId ID của nhân viên khôi phục
   */
  async restore(id: number, employeeId: number): Promise<void> {
    return this.restoreBulk([id], employeeId);
  }

  /**
   * Khôi phục nhiều type agents đã xóa mềm (bulk)
   * @param ids Danh sách ID của các type agent cần khôi phục
   * @param employeeId ID của nhân viên khôi phục
   */
  async restoreBulk(ids: number[], employeeId: number): Promise<void> {
    try {
      this.logger.log(`Bulk restoring type agents: ${ids.join(', ')} by employee ${employeeId}`);

      if (!ids || ids.length === 0) {
        this.logger.warn('No IDs provided for bulk restore');
        return;
      }

      // Khôi phục từng type agent
      const results = await Promise.allSettled(
        ids.map(id => this.typeAgentRepository.restoreTypeAgent(id))
      );

      // Đếm số lượng thành công và thất bại
      const successCount = results.filter(result => result.status === 'fulfilled' && result.value).length;
      const failedCount = results.length - successCount;

      this.logger.log(`Bulk restore completed. Success: ${successCount}, Failed: ${failedCount}`);

      if (successCount === 0) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND, 'Không tìm thấy type agent nào để khôi phục');
      }

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error in bulk restore type agents: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_DELETE_FAILED);
    }
  }



  /**
   * Khôi phục nhiều type agents đã xóa mềm
   * @param ids Danh sách ID của các type agents cần khôi phục
   * @returns Số lượng type agents đã khôi phục thành công
   */
  async bulkRestore(ids: number[]): Promise<number> {
    try {
      let restoredCount = 0;

      // Xử lý từng type agent
      for (const id of ids) {
        try {
          const success = await this.typeAgentRepository.restoreTypeAgent(id);
          if (success) {
            restoredCount++;
          } else {
            this.logger.warn(`Type agent ${id} không thể khôi phục (có thể không tồn tại hoặc chưa bị xóa)`);
          }
        } catch (error) {
          this.logger.error(`Lỗi khi khôi phục type agent ${id}: ${error.message}`);
          // Tiếp tục với các ID khác
        }
      }

      return restoredCount;
    } catch (error) {
      this.logger.error(`Error bulk restoring type agents: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_DELETE_FAILED);
    }
  }

  /**
   * Chuyển đổi TypeAgent thành TypeAgentListItemDto
   * @param typeAgent Entity TypeAgent
   * @param toolIds Danh sách tool IDs
   * @param modelIds Danh sách model IDs
   * @returns TypeAgentListItemDto
   */
  private async mapToListItemDto(
    typeAgent: TypeAgent,
    toolIds: string[] = [],
    modelIds: string[] = [],
  ): Promise<TypeAgentListItemDto> {

    return {
      id: typeAgent.id,
      name: typeAgent.name,
      description: typeAgent.description,
      createdAt: typeAgent.createdAt,
      status: typeAgent.status,
      toolIds,
      modelIds,
    };
  }

  /**
   * Chuyển đổi TypeAgent thành TypeAgentDetailDto
   * @param typeAgent Entity TypeAgent
   * @param agentSystems Danh sách agent systems
   * @param employeeInfo Thông tin employee
   * @param toolIds Danh sách tool IDs
   * @param modelIds Danh sách model IDs
   * @returns TypeAgentDetailDto
   */
  private async mapToDetailDto(
    typeAgent: TypeAgent,
    agentSystems: { id: string; name: string; avatar: string | null; model: string }[],
    employeeInfo: {
      created: EmployeeInfoDto | undefined;
      updated: EmployeeInfoDto | undefined;
    },
    toolIds: string[] = [],
    modelIds: string[] = [],
  ): Promise<TypeAgentDetailDto> {

    return {
      id: typeAgent.id,
      name: typeAgent.name,
      description: typeAgent.description,
      defaultConfig: typeAgent.config,
      status: typeAgent.status,
      createdAt: typeAgent.createdAt,
      updatedAt: typeAgent.updatedAt,
      agentSystems: agentSystems,
      created: employeeInfo.created,
      updated: employeeInfo.updated,
      toolIds,
      modelIds,
    };
  }

  /**
   * Chuyển đổi TypeAgent thành TypeAgentTrashItemDto
   * @param typeAgent Entity TypeAgent đã bị xóa
   * @returns TypeAgentTrashItemDto
   */
  private async mapToTrashItemDto(typeAgent: TypeAgent): Promise<TypeAgentTrashItemDto> {
    // Lấy thông tin người xóa
    let deletedInfo: EmployeeInfoSimpleDto | null = null;
    if (typeAgent.deletedBy) {
      try {
        deletedInfo = await this.employeeInfoService.getEmployeeInfo(typeAgent.deletedBy);
      } catch (error) {
        this.logger.warn(`Cannot get deleted employee info for ID ${typeAgent.deletedBy}`);
        deletedInfo = {
          id: typeAgent.deletedBy,
          name: 'Unknown',
          avatar: null,
        };
      }
    }

    return {
      id: typeAgent.id,
      name: typeAgent.name,
      description: typeAgent.description,
      createdAt: typeAgent.createdAt,
      deletedAt: typeAgent.deletedAt || 0,
      deleted: deletedInfo || {
        id: 0,
        name: 'Unknown',
        avatar: null,
      },
    };
  }

  /**
   * Lấy thông tin employee cho type agent
   * @param typeAgent Entity TypeAgent
   * @returns Thông tin employee
   */
  private async getEmployeeInfoForTypeAgent(typeAgent: TypeAgent): Promise<{
    created: EmployeeInfoDto | undefined;
    updated: EmployeeInfoDto | undefined;
    deleted: EmployeeInfoDto | undefined;
  }> {
    const result = {
      created: undefined as EmployeeInfoDto | undefined,
      updated: undefined as EmployeeInfoDto | undefined,
      deleted: undefined as EmployeeInfoDto | undefined,
    };

    // Lấy thông tin người tạo
    if (typeAgent.createdBy) {
      try {
        const createdInfo = await this.employeeInfoService.getEmployeeInfo(typeAgent.createdBy);
        result.created = {
          employeeId: createdInfo.id,
          name: createdInfo.name,
          avatar: createdInfo.avatar || null,
        };
      } catch (error) {
        this.logger.warn(`Cannot get created employee info for ID ${typeAgent.createdBy}`);
      }
    }

    // Lấy thông tin người cập nhật
    if (typeAgent.updatedBy && typeAgent.updatedBy !== typeAgent.createdBy) {
      try {
        const updatedInfo = await this.employeeInfoService.getEmployeeInfo(typeAgent.updatedBy);
        result.updated = {
          employeeId: updatedInfo.id,
          name: updatedInfo.name,
          avatar: updatedInfo.avatar || null,
        };
      } catch (error) {
        this.logger.warn(`Cannot get updated employee info for ID ${typeAgent.updatedBy}`);
      }
    }

    // Lấy thông tin người xóa
    if (typeAgent.deletedBy) {
      try {
        const deletedInfo = await this.employeeInfoService.getEmployeeInfo(typeAgent.deletedBy);
        result.deleted = {
          employeeId: deletedInfo.id,
          name: deletedInfo.name,
          avatar: deletedInfo.avatar || null,
        };
      } catch (error) {
        this.logger.warn(`Cannot get deleted employee info for ID ${typeAgent.deletedBy}`);
      }
    }

    return result;
  }

  // ===== AGENT SYSTEMS MANAGEMENT METHODS =====

  /**
   * Thêm agent systems vào type agent
   * @param typeAgentId ID của type agent
   * @param addDto Dữ liệu agent systems cần thêm
   * @param employeeId ID của nhân viên thực hiện
   */
  @Transactional()
  async addAgentSystems(
    typeAgentId: number,
    addDto: AddAgentSystemsDto,
    employeeId: number,
  ): Promise<{ added: number }> {
    // Kiểm tra type agent có tồn tại không
    const typeAgent = await this.typeAgentRepository.findById(typeAgentId);
    if (!typeAgent) {
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
    }

    // Kiểm tra type agent đã bị xóa mềm hay chưa
    if (typeAgent.deletedAt) {
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_ALREADY_DELETED);
    }

    // Kiểm tra agent systems có tồn tại không
    const existingAgentSystemIds = await this.agentSystemRepository.findExistingIds(
      addDto.agentSystemIds,
    );
    if (existingAgentSystemIds.length !== addDto.agentSystemIds.length) {
      const missingIds = addDto.agentSystemIds.filter(id => !existingAgentSystemIds.includes(id));
      this.logger.warn(`Agent systems not found: ${missingIds.join(', ')}`);
      throw new AppException(AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND);
    }

    // Kiểm tra không được chọn supervisor agents
    const supervisorIds = await this.agentSystemRepository.findSupervisorIds(
      addDto.agentSystemIds,
    );
    if (supervisorIds.length > 0) {
      this.logger.warn(`Cannot assign supervisor agents to type agent: ${supervisorIds.join(', ')}`);
      throw new AppException(AGENT_ERROR_CODES.AGENT_SYSTEM_SUPERVISOR_NOT_ALLOWED);
    }

    try {
      // Lấy danh sách agent systems hiện tại
      const currentAgentSystems = await this.typeAgentAgentSystemRepository.findAgentsByTypeId(typeAgentId);
      const currentAgentIds = currentAgentSystems.map(agent => agent.id);

      // Tìm các agent systems mới cần thêm (chưa có trong type agent)
      const agentIdsToAdd = addDto.agentSystemIds.filter(id => !currentAgentIds.includes(id));

      if (agentIdsToAdd.length === 0) {
        this.logger.log(`No new agent systems to add for type agent ${typeAgentId}`);
        return { added: 0 };
      }

      // Thêm từng agent system
      for (const agentId of agentIdsToAdd) {
        await this.typeAgentAgentSystemRepository.save({
          typeId: typeAgentId,
          agentId: agentId,
        });
      }

      this.logger.log(`Added ${agentIdsToAdd.length} agent systems to type agent ${typeAgentId}`);
      return { added: agentIdsToAdd.length };

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error adding agent systems to type agent: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Gỡ agent systems khỏi type agent
   * @param typeAgentId ID của type agent
   * @param removeDto Dữ liệu agent systems cần gỡ
   * @param employeeId ID của nhân viên thực hiện
   */
  @Transactional()
  async removeAgentSystems(
    typeAgentId: number,
    removeDto: RemoveAgentSystemsDto,
    employeeId: number,
  ): Promise<{ removed: number }> {
    // Kiểm tra type agent có tồn tại không
    const typeAgent = await this.typeAgentRepository.findById(typeAgentId);
    if (!typeAgent) {
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
    }

    // Kiểm tra type agent đã bị xóa mềm hay chưa
    if (typeAgent.deletedAt) {
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_ALREADY_DELETED);
    }

    try {
      let removedCount = 0;

      // Gỡ từng agent system
      for (const agentId of removeDto.agentSystemIds) {
        const success = await this.typeAgentAgentSystemRepository.unlinkTypeFromAgent(
          typeAgentId,
          agentId,
        );
        if (success) {
          removedCount++;
        }
      }

      this.logger.log(`Removed ${removedCount} agent systems from type agent ${typeAgentId}`);
      return { removed: removedCount };

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error removing agent systems from type agent: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_UPDATE_FAILED);
    }
  }
  /**
   * Lấy danh sách agent systems của type agent
   * @param typeAgentId ID của type agent
   * @param queryDto Tham số truy vấn
   */
  async getAgentSystems(
    typeAgentId: number,
    queryDto: TypeAgentSystemsQueryDto,
  ): Promise<PaginatedResult<TypeAgentSystemItemDto>> {
    // Kiểm tra type agent có tồn tại không
    const typeAgent = await this.typeAgentRepository.findById(typeAgentId);
    if (!typeAgent) {
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
    }

    try {
      // Sử dụng database-level pagination
      const result = await this.typeAgentAgentSystemRepository.findAgentsByTypeIdPaginated(
        typeAgentId,
        queryDto.page,
        queryDto.limit,
        queryDto.search,
        queryDto.sortBy || 'nameCode',
        queryDto.sortDirection || 'ASC',
      );

      // Map to DTO
      const items = result.items.map(agent => this.mapToAgentSystemItemDto(agent));

      return {
        items,
        meta: result.meta,
      };

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error getting agent systems for type agent: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_FETCH_FAILED);
    }
  }

  /**
   * Map agent system data to DTO
   * @param agentData Raw agent system data
   * @returns TypeAgentSystemItemDto
   */
  private mapToAgentSystemItemDto(agentData: any): TypeAgentSystemItemDto {
    const dto = new TypeAgentSystemItemDto();
    dto.id = agentData.id;
    dto.nameCode = agentData.nameCode;
    dto.description = agentData.description || '';
    dto.active = agentData.active || false;
    dto.isSupervisor = agentData.isSupervisor || false;
    dto.name = agentData.name || 'Unknown Agent';
    dto.avatar = this.cdnService.generateUrlView(agentData.avatar, TimeIntervalEnum.ONE_DAY) || null;
    dto.modelId = agentData.modelId || 'Unknown Model';
    dto.provider = agentData.provider || 'Unknown Provider';
    return dto;
  }

  // ===== TOOLS MANAGEMENT METHODS =====

  /**
   * Lấy danh sách tools của type agent
   * @param typeAgentId ID của type agent
   * @returns Danh sách tools
   */
  async getTools(typeAgentId: number): Promise<{ typeAgentId: number; toolIds: string[] }> {
    try {
      this.logger.log(`Getting tools for type agent: ${typeAgentId}`);

      // Validate type agent exists
      const typeAgent = await this.typeAgentRepository.findById(typeAgentId);
      if (!typeAgent) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }

      // Lấy danh sách tool IDs
      const toolIds = await this.typeAgentToolsRepository.getToolIdsByTypeAgent(typeAgentId);

      return {
        typeAgentId,
        toolIds,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error getting tools for type agent ${typeAgentId}: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_FETCH_FAILED);
    }
  }

  /**
   * Thêm tools vào type agent
   * @param typeAgentId ID của type agent
   * @param toolIds Danh sách tool IDs
   */
  @Transactional()
  async addTools(typeAgentId: number, toolIds: string[]): Promise<void> {
    try {
      this.logger.log(`Adding tools to type agent ${typeAgentId}: ${toolIds.join(', ')}`);

      // Validate type agent exists
      const typeAgent = await this.typeAgentRepository.findById(typeAgentId);
      if (!typeAgent) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }

      // Thêm từng tool vào type agent
      for (const toolId of toolIds) {
        await this.typeAgentToolsRepository.addToolToTypeAgent(typeAgentId, toolId);
      }

      this.logger.log(`Successfully added ${toolIds.length} tools to type agent ${typeAgentId}`);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error adding tools to type agent ${typeAgentId}: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Xóa tools khỏi type agent
   * @param typeAgentId ID của type agent
   * @param toolIds Danh sách tool IDs
   */
  @Transactional()
  async removeTools(typeAgentId: number, toolIds: string[]): Promise<void> {
    try {
      this.logger.log(`Removing tools from type agent ${typeAgentId}: ${toolIds.join(', ')}`);

      // Validate type agent exists
      const typeAgent = await this.typeAgentRepository.findById(typeAgentId);
      if (!typeAgent) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }

      // Xóa từng tool khỏi type agent
      for (const toolId of toolIds) {
        await this.typeAgentToolsRepository.removeToolFromTypeAgent(typeAgentId, toolId);
      }

      this.logger.log(`Successfully removed ${toolIds.length} tools from type agent ${typeAgentId}`);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error removing tools from type agent ${typeAgentId}: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  // ===== MODELS MANAGEMENT METHODS =====

  /**
   * Lấy danh sách models của type agent
   * @param typeAgentId ID của type agent
   * @returns Danh sách models
   */
  async getModels(typeAgentId: number): Promise<{ typeAgentId: number; modelRegistryIds: string[] }> {
    try {
      this.logger.log(`Getting models for type agent: ${typeAgentId}`);

      // Validate type agent exists
      const typeAgent = await this.typeAgentRepository.findById(typeAgentId);
      if (!typeAgent) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }

      // Lấy danh sách model registry IDs
      const modelRegistryIds = await this.typeAgentModelsRepository.getModelRegistryIdsByTypeAgent(typeAgentId);

      return {
        typeAgentId,
        modelRegistryIds,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error getting models for type agent ${typeAgentId}: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_FETCH_FAILED);
    }
  }

  /**
   * Thay thế tất cả models của type agent
   * @param typeAgentId ID của type agent
   * @param modelRegistryIds Danh sách model registry IDs mới
   */
  @Transactional()
  async replaceModels(typeAgentId: number, modelRegistryIds: string[]): Promise<void> {
    try {
      this.logger.log(`Replacing models for type agent ${typeAgentId} with: ${modelRegistryIds.join(', ')}`);

      // Validate type agent exists
      const typeAgent = await this.typeAgentRepository.findById(typeAgentId);
      if (!typeAgent) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }

      // Thay thế tất cả models
      await this.typeAgentModelsRepository.replaceModelsForTypeAgent(typeAgentId, modelRegistryIds);

      this.logger.log(`Successfully replaced models for type agent ${typeAgentId} with ${modelRegistryIds.length} models`);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error replacing models for type agent ${typeAgentId}: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Xóa models khỏi type agent
   * @param typeAgentId ID của type agent
   * @param modelRegistryIds Danh sách model registry IDs
   */
  @Transactional()
  async removeModels(typeAgentId: number, modelRegistryIds: string[]): Promise<void> {
    try {
      this.logger.log(`Removing models from type agent ${typeAgentId}: ${modelRegistryIds.join(', ')}`);

      // Validate type agent exists
      const typeAgent = await this.typeAgentRepository.findById(typeAgentId);
      if (!typeAgent) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }

      // Xóa từng model khỏi type agent
      for (const modelRegistryId of modelRegistryIds) {
        await this.typeAgentModelsRepository.removeModelFromTypeAgent(typeAgentId, modelRegistryId);
      }

      this.logger.log(`Successfully removed ${modelRegistryIds.length} models from type agent ${typeAgentId}`);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error removing models from type agent ${typeAgentId}: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }
}
