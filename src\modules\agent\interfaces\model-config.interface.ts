import { SamplingParameterEnum } from '@modules/models/constants/model-capabilities.enum';

/**
 * Interface định nghĩa cấu hình model cho agent
 */
export interface ModelConfig {
  /**
   * Giá trị temperature cho model (0-2)
   */
  temperature?: number;

  /**
   * Gi<PERSON> trị top_p cho model (0-1)
   */
  top_p?: number;

  /**
   * Giá trị top_k cho model
   */
  top_k?: number;

  /**
   * Số token tối đa cho kết quả
   */
  max_tokens?: number;

  /**
   * <PERSON><PERSON> s<PERSON>ch các tham số sampling được model hỗ trợ
   */
  allowedSamplingParameters?: SamplingParameterEnum[];
}
