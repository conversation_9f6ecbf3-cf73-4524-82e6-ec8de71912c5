import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { S3Service } from '@shared/services/s3.service';
import { CdnService } from '@shared/services/cdn.service';
import { ToolsModule } from '@/modules/tools/tools.module';
import { EmployeeModule } from '@modules/employee/employee.module';
import { KnowledgeFilesModule } from '@modules/data/knowledge-files/knowledge-files.module';
import { ModelsAdminModule } from '@modules/models/admin/models-admin.module';
import { Agent, AgentUser, TypeAgent, AgentStrategy, AgentStrategyUser } from '@modules/agent/entities';
import { Product } from '@modules/marketplace/entities/product.entity';
import {
  AdminToolRepository,
  AdminToolVersionRepository,
  TypeAgentRepository as ToolsTypeAgentRepository,
  UserToolRepository,
} from '@/modules/tools/repositories';
import { ToolFunctionValidationHelper } from '@/modules/tools/helpers/tool-function-validation.helper';
import {
  AdminAgentSystemController,
  AdminTypeAgentController,
  AgentRankAdminController,
  AdminUserAgentController,
  McpSystemAdminController,
  AgentStrategyController,
} from '@modules/agent/admin/controllers';
import {
  AdminAgentSystemService,
  AdminTypeAgentService,
  AgentRankAdminService,
  AdminUserAgentService,
  McpSystemAdminService,
  AgentStrategyAdminService,
} from '@modules/agent/admin/services';
import { TypeAgentRepository, AgentRankRepository, AgentStrategyRepository, AgentUserRepository, TypeAgentAgentSystemRepository, AgentMemoriesRepository, TypeAgentToolsRepository, TypeAgentModelsRepository } from '@modules/agent/repositories';
import { McpSystemsRepository } from '@modules/agent/repositories/mcp-systems.repository';
import { AgentSystemMcpRepository } from '@modules/agent/repositories/agent-system-mcp.repository';
import { SystemModelsRepository } from '@modules/models/repositories/system-models.repository';
import { ProductRepository } from '@modules/marketplace/repositories/product.repository';
import { AgentRank } from '@modules/agent/entities';
import { AdminAgentTemplateController } from '@modules/agent/admin/controllers';
import { AdminAgentTemplateService } from '@modules/agent/admin/services';
import { AgentRepository } from '@modules/agent/repositories/agent.repository';
import { AgentSystemRepository } from '@modules/agent/repositories/agent-system.repository';
import { AgentTemplateRepository } from '@modules/agent/repositories/agent-template.repository';

import {
  VectorStoreFileRepository,
  VectorStoreRepository,
} from '@modules/data/knowledge-files/repositories';

@Module({
  imports: [
    TypeOrmModule.forFeature([AgentRank, Agent, AgentUser, TypeAgent, AgentStrategy, AgentStrategyUser, Product]),
    ToolsModule,
    EmployeeModule,
    KnowledgeFilesModule,
    ModelsAdminModule,
  ],
  controllers: [
    AdminTypeAgentController,
    AdminAgentSystemController,
    AdminAgentTemplateController,
    AgentRankAdminController,
    AdminUserAgentController,
    McpSystemAdminController,
    AgentStrategyController,
  ],
  providers: [
    S3Service,
    CdnService,
    ToolFunctionValidationHelper,
    AdminToolRepository,
    AdminToolVersionRepository,
    UserToolRepository,
    ToolsTypeAgentRepository,
    AdminTypeAgentService,
    TypeAgentRepository,
    AgentUserRepository,
    TypeAgentAgentSystemRepository,
    TypeAgentToolsRepository,
    TypeAgentModelsRepository,
    AdminAgentSystemService,
    AdminAgentTemplateService,
    AgentRepository,
    AgentSystemRepository,
    AgentSystemMcpRepository,
    AgentTemplateRepository,
    AgentMemoriesRepository,
    McpSystemsRepository,
    SystemModelsRepository,
    ProductRepository,
    VectorStoreRepository,
    VectorStoreFileRepository,
    AgentRankAdminService,
    AgentRankRepository,
    AdminUserAgentService,
    McpSystemAdminService,
    AgentStrategyAdminService,
    AgentStrategyRepository,
  ],
  exports: [
    AdminTypeAgentService,
    AdminAgentSystemService,
    AdminAgentTemplateService,
    AgentRankAdminService,
    AdminUserAgentService,
    McpSystemAdminService,
    AgentStrategyAdminService,
  ],
})
export class AgentAdminModule {}
