import {ApiProperty, ApiPropertyOptional} from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
  MaxLength,
  ValidateNested,
} from 'class-validator';
import {Type} from 'class-transformer';
import {TypeAgentStatus} from '@modules/agent/constants';
import {TypeAgentConfig} from '@modules/agent/interfaces/type-agent-config.interface';

/**
 * DTO cho cấu hình loại agent
 */
export class TypeAgentConfigDto implements TypeAgentConfig {
  /**
   * <PERSON><PERSON> hồ sơ không
   */
  @ApiProperty({
    description: '<PERSON><PERSON> hồ sơ không',
    example: true,
  })
  @IsBoolean()
  enableAgentProfileCustomization: boolean;

  /**
   * C<PERSON> đầu ra không
   */
  @ApiProperty({
    description: 'Có đầu ra qua Messenger không',
    example: true,
  })
  @IsBoolean()
  enableOutputToMessenger: boolean;

  @ApiProperty({
    description: '<PERSON><PERSON> đầu ra qua Live Chat trên website không',
    example: true,
  })
  @IsBoolean()
  enableOutputToWebsiteLiveChat: boolean;

  /**
   * Có chuyển đổi không
   */
  @ApiProperty({
    description: 'Có chuyển đổi không',
    example: false,
  })
  @IsBoolean()
  enableTaskConversionTracking: boolean;

  /**
   * Có tài nguyên không
   */
  @ApiProperty({
    description: 'Có tài nguyên không',
    example: true,
  })
  @IsBoolean()
  enableResourceUsage: boolean;

  /**
   * Có chiến lược không
   */
  @ApiProperty({
    description: 'Có chiến lược không',
    example: true,
  })
  @IsBoolean()
  enableDynamicStrategyExecution: boolean;

  /**
   * Có đa agent không
   */
  @ApiProperty({
    description: 'Có đa agent không',
    example: true,
  })
  @IsBoolean()
  enableMultiAgentCollaboration: boolean;

  /**
   * Có đầu ra qua Zalo OA không
   */
  @ApiProperty({
    description: 'Có đầu ra qua Zalo OA không',
    example: true,
  })
  @IsBoolean()
  enableOutputToZaloOA: boolean;
}

/**
 * DTO cho việc tạo loại agent mới
 */
export class CreateTypeAgentDto {
  /**
   * Tên loại agent
   */
  @ApiProperty({
    description: 'Tên loại agent',
    example: 'Chatbot Agent',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  /**
   * Mô tả chi tiết về loại agent
   */
  @ApiPropertyOptional({
    description: 'Mô tả chi tiết về loại agent',
    example: 'Loại agent hỗ trợ chat với người dùng',
  })
  @IsString()
  @IsOptional()
  description: string | null;

  /**
   * Cấu hình mặc định cho loại agent
   */
  @ApiProperty({
    description: 'Cấu hình mặc định cho loại agent',
    example: {
      enableAgentProfileCustomization: true,
      enableOutputToMessenger: true,
      enableOutputToWebsiteLiveChat: true,
      enableTaskConversionTracking: false,
      enableResourceUsage: true,
      enableDynamicStrategyExecution: true,
      enableMultiAgentCollaboration: false,
      enableOutputToZaloOA: true,
    },
  })
  @ValidateNested()
  @Type(() => TypeAgentConfigDto)
  defaultConfig: TypeAgentConfigDto;

  /**
   * Trạng thái của loại agent
   */
  @ApiPropertyOptional({
    description: 'Trạng thái của loại agent',
    enum: TypeAgentStatus,
    default: TypeAgentStatus.DRAFT,
  })
  @IsEnum(TypeAgentStatus)
  status: TypeAgentStatus;

  /**
   * Danh sách ID của các agent system
   */
  @ApiProperty({
    description: 'Danh sách ID của các agent system',
    example: ['agent-system-uuid-1', 'agent-system-uuid-2'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  agentSystems: string[];

  /**
   * Danh sách tool IDs cho type agent
   */
  @ApiPropertyOptional({
    description: 'Danh sách tool IDs cho type agent',
    example: ['550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true, message: 'Mỗi toolId phải là UUID hợp lệ' })
  toolIds?: string[];

  /**
   * Danh sách model registry IDs cho type agent
   */
  @ApiPropertyOptional({
    description: 'Danh sách model registry IDs cho type agent',
    example: ['550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true, message: 'Mỗi modelRegistryId phải là UUID hợp lệ' })
  modelRegistryIds?: string[];
}
