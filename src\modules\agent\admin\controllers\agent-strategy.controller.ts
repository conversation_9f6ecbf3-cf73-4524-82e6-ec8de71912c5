
import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  ParseUUIDPipe
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  getSchemaPath, ApiBody,
} from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { CurrentEmployee } from '@modules/auth/decorators/current-employee.decorator';
import { JWTPayload } from '@modules/auth/interfaces';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { KNOWLEDGE_FILE_ERROR_CODES } from '@modules/data/knowledge-files/exceptions';
import { MODELS_ERROR_CODES } from '@modules/models/exceptions';
import { AgentStrategyAdminService } from '../services/agent-strategy.service';
import {
  CreateAgentStrategyDto,
  UpdateAgentStrategyDto,
  QueryAgentStrategyDto,
  AgentStrategyResponseDto,
  AgentStrategyDetailResponseDto
} from '../dto/agent-strategy';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller xử lý các API liên quan đến chiến lược agent cho admin
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_AGENT_STRATEGY)
@Controller('admin/agent-strategy')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
export class AgentStrategyController {
  constructor(
    private readonly agentStrategyService: AgentStrategyAdminService,
  ) {}

  /**
   * Lấy danh sách chiến lược agent có phân trang
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách chiến lược agent có phân trang',
    description: 'API để lấy danh sách tất cả chiến lược agent với khả năng tìm kiếm và phân trang'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách chiến lược agent thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: {
              allOf: [
                { $ref: getSchemaPath(PaginatedResult) },
                {
                  properties: {
                    items: {
                      type: 'array',
                      items: { $ref: getSchemaPath(AgentStrategyResponseDto) }
                    }
                  }
                }
              ]
            }
          }
        }
      ]
    }
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.STRATEGY_NOT_FOUND
  )
  async getStrategies(
    @Query() queryDto: QueryAgentStrategyDto
  ): Promise<ApiResponseDto<PaginatedResult<AgentStrategyResponseDto>>> {
    const result = await this.agentStrategyService.getStrategies(queryDto);
    return ApiResponseDto.success(result, 'Lấy danh sách chiến lược agent thành công');
  }

  /**
   * Lấy danh sách chiến lược agent đã xóa có phân trang
   */
  @Get('trash')
  @ApiOperation({
    summary: 'Lấy danh sách chiến lược agent đã xóa có phân trang',
    description: 'API để lấy danh sách tất cả chiến lược agent đã bị xóa mềm'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách chiến lược agent đã xóa thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: {
              allOf: [
                { $ref: getSchemaPath(PaginatedResult) },
                {
                  properties: {
                    items: {
                      type: 'array',
                      items: { $ref: getSchemaPath(AgentStrategyResponseDto) }
                    }
                  }
                }
              ]
            }
          }
        }
      ]
    }
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.STRATEGY_NOT_FOUND
  )
  async getDeletedStrategies(
    @Query() queryDto: QueryAgentStrategyDto
  ): Promise<ApiResponseDto<PaginatedResult<AgentStrategyResponseDto>>> {
    const result = await this.agentStrategyService.getDeletedStrategies(queryDto);
    return ApiResponseDto.success(result, 'Lấy danh sách chiến lược agent đã xóa thành công');
  }

  /**
   * Lấy chi tiết chiến lược agent theo ID
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Lấy chi tiết chiến lược agent theo ID',
    description: 'API để lấy thông tin chi tiết của một chiến lược agent cụ thể'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của chiến lược agent',
    example: 'agent-uuid-123'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy chi tiết chiến lược agent thành công',
    schema: ApiResponseDto.getSchema(AgentStrategyDetailResponseDto)
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.STRATEGY_NOT_FOUND
  )
  async getStrategyById(
    @Param('id', ParseUUIDPipe) id: string
  ): Promise<ApiResponseDto<AgentStrategyDetailResponseDto>> {
    const result = await this.agentStrategyService.getStrategyById(id);
    return ApiResponseDto.success(result, 'Lấy chi tiết chiến lược agent thành công');
  }

  /**
   * Tạo chiến lược agent mới
   */
  @Post()
  @ApiOperation({
    summary: 'Tạo chiến lược agent mới',
    description: `API để tạo một chiến lược agent mới trong hệ thống.

**Lưu ý quan trọng:**
- \`systemModelId\`: **BẮT BUỘC** - Phải là UUID hợp lệ của system model tồn tại trong hệ thống
- \`vectorStoreId\`: Nếu cung cấp, phải là ID hợp lệ tồn tại trong hệ thống
- \`content\` và \`exampleDefault\`: Bắt buộc phải có ít nhất 1 phần tử
- Mỗi step trong content phải có \`stepOrder\` (số nguyên dương) và \`content\` (chuỗi không rỗng)

**Example request body:**
\`\`\`json
{
  "name": "AI Assistant Strategy",
  "modelConfig": {
    "temperature": 1.0,
    "top_p": 1.0,
    "top_k": 1.0
  },
  "instruction": "Bạn là một trợ lý AI chuyên về chiến lược kinh doanh",
  "systemModelId": "550e8400-e29b-41d4-a716-************",
  "content": [
    {
      "stepOrder": 1,
      "content": "Bước đầu tiên: Phân tích yêu cầu"
    }
  ],
  "exampleDefault": [
    {
      "stepOrder": 1,
      "content": "Ví dụ: Khi người dùng hỏi về thời tiết"
    }
  ]
}
\`\`\``
  })
  @ApiResponse({
    status: 201,
    description: 'Tạo chiến lược agent thành công',
    schema: ApiResponseDto.getSchema(AgentStrategyResponseDto),
    examples: {
      success: {
        summary: 'Tạo thành công',
        value: {
          success: true,
          message: 'Tạo chiến lược agent thành công',
          result: {
            id: 'd47ba20d-7c97-4ac0-9eb7-e5e7f5d74c4f',
            name: 'AI Assistant Strategy',
            avatar: 'https://cdn.redai.vn/agents/avatars/agent-avatar.jpg?expires=1750152880&signature=nGxQfY3rtfZAOrxl4MMkxMPLaBY',
            modelId: 'gpt4o'
          }
        }
      }
    }
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.STRATEGY_CREATION_FAILED, // Lỗi chung khi tạo strategy
    KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_NOT_FOUND, // vectorStoreId không tồn tại
    MODELS_ERROR_CODES.MODEL_NOT_FOUND // systemModelId không tồn tại
  )
  async createStrategy(
    @Body() createDto: CreateAgentStrategyDto,
    @CurrentEmployee() employee: JWTPayload
  ): Promise<ApiResponseDto<{ id: string; avatarUploadUrl: string | null; }>> {
    const result = await this.agentStrategyService.createStrategy(createDto, Number(employee.id));
    return ApiResponseDto.created(result, 'Tạo chiến lược agent thành công');
  }

  /**
   * Cập nhật chiến lược agent
   */
  @Put(':id')
  @ApiOperation({
    summary: 'Cập nhật chiến lược agent',
    description: `API để cập nhật thông tin của một chiến lược agent đã tồn tại.

**Các trường có thể cập nhật:**

**Thông tin Agent:**
- \`name\`: Tên hiển thị của agent
- \`avatarMimeType\`: MIME type để upload avatar mới (sẽ trả về URL upload)
- \`instruction\`: Hướng dẫn hoặc system prompt cho agent
- \`modelConfig\`: Cấu hình AI model (temperature, top_p, top_k)
- \`vectorStoreId\`: ID của kho vector (có thể set null để xóa liên kết)

**Thông tin Strategy:**
- \`content\`: Nội dung chiến lược dạng array các bước
- \`exampleDefault\`: Các ví dụ mẫu dạng array các bước
- \`systemModelId\`: ID tham chiếu đến system model (có thể set null để xóa liên kết)

**Lưu ý quan trọng:**
- Tất cả các trường đều optional trong update
- \`vectorStoreId\` và \`systemModelId\`: Nếu cung cấp (không phải null), phải là ID hợp lệ tồn tại trong hệ thống
- \`content\` và \`exampleDefault\`: Nếu cung cấp, phải có ít nhất 1 phần tử hợp lệ
- Có thể set \`vectorStoreId\` hoặc \`systemModelId\` thành \`null\` để xóa liên kết`
  })
  @ApiParam({
    name: 'id',
    description: 'ID của chiến lược agent',
    example: 'ae3d38e3-c102-4d72-9d68-636a31660716'
  })
  @ApiBody({
    description: 'Dữ liệu cập nhật chiến lược agent',
    type: UpdateAgentStrategyDto,
    examples: {
      updateBasicInfo: {
        summary: 'Cập nhật thông tin cơ bản',
        description: 'Ví dụ cập nhật tên và instruction của agent',
        value: {
          name: 'AI Assistant Strategy Updated',
          instruction: 'Bạn là một trợ lý AI chuyên về chiến lược kinh doanh nâng cao'
        }
      },
      updateWithAvatar: {
        summary: 'Cập nhật với avatar mới',
        description: 'Ví dụ cập nhật avatar, model config và vector store',
        value: {
          name: 'AI Assistant Strategy',
          avatarMimeType: 'image/jpeg',
          instruction: 'Bạn là một trợ lý AI chuyên về chiến lược kinh doanh',
          vectorStoreId: 'vector-store-456',
          modelConfig: {
            temperature: 0.8,
            top_p: 0.9,
            top_k: 1.0
          }
        }
      },
      updateStrategy: {
        summary: 'Cập nhật nội dung chiến lược',
        description: 'Ví dụ cập nhật content và example của strategy',
        value: {
          content: [
            { stepOrder: 1, content: 'Bước đầu tiên: Phân tích yêu cầu chi tiết' },
            { stepOrder: 2, content: 'Bước hai: Xử lý thông tin và đưa ra giải pháp' }
          ],
          exampleDefault: [
            { stepOrder: 1, content: 'Ví dụ: Khi khách hàng hỏi về sản phẩm' },
            { stepOrder: 2, content: 'Ví dụ: Khi khách hàng cần hỗ trợ kỹ thuật' }
          ],
          systemModelId: '550e8400-e29b-41d4-a716-************'
        }
      },
      removeConnections: {
        summary: 'Xóa liên kết',
        description: 'Ví dụ xóa liên kết với vector store và system model bằng cách set null',
        value: {
          vectorStoreId: null,
          systemModelId: null
        }
      },
      updateComplete: {
        summary: 'Cập nhật đầy đủ',
        description: 'Ví dụ cập nhật tất cả các trường có thể',
        value: {
          name: 'AI Assistant Strategy Complete',
          avatarMimeType: 'image/png',
          instruction: 'Bạn là một trợ lý AI toàn diện với khả năng xử lý đa dạng yêu cầu',
          vectorStoreId: 'vector-store-789',
          modelConfig: {
            temperature: 0.7,
            top_p: 0.95,
            top_k: 0.8
          },
          content: [
            { stepOrder: 1, content: 'Bước 1: Tiếp nhận và phân tích yêu cầu từ người dùng' },
            { stepOrder: 2, content: 'Bước 2: Xử lý thông tin và tìm kiếm dữ liệu liên quan' },
            { stepOrder: 3, content: 'Bước 3: Đưa ra câu trả lời chi tiết và chính xác' }
          ],
          exampleDefault: [
            { stepOrder: 1, content: 'Ví dụ: Khi khách hàng hỏi về sản phẩm mới' },
            { stepOrder: 2, content: 'Ví dụ: Khi khách hàng cần hỗ trợ kỹ thuật phức tạp' },
            { stepOrder: 3, content: 'Ví dụ: Khi khách hàng muốn tư vấn chiến lược' }
          ],
          systemModelId: '550e8400-e29b-41d4-a716-************'
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật chiến lược agent thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: {
              type: 'object',
              properties: {
                id: {
                  type: 'string',
                  description: 'ID của agent đã cập nhật',
                  example: 'ae3d38e3-c102-4d72-9d68-636a31660716'
                },
                avatarUploadUrl: {
                  type: 'string',
                  nullable: true,
                  description: 'URL upload avatar (chỉ có khi cập nhật avatar)',
                  example: 'https://cdn.redai.vn/3/agents/1750128387074-a8a6febe-2138-4229-9e63-cd8794aeb900?expires=1750152880&signature=nGxQfY3rtfZAOrxl4MMkxMPLaBY'
                }
              }
            }
          }
        }
      ]
    }
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.STRATEGY_NOT_FOUND, // Strategy không tồn tại
    AGENT_ERROR_CODES.STRATEGY_UPDATE_FAILED, // Lỗi chung khi cập nhật
    KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_NOT_FOUND, // vectorStoreId không tồn tại
    MODELS_ERROR_CODES.MODEL_NOT_FOUND // systemModelId không tồn tại
  )
  async updateStrategy(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDto: UpdateAgentStrategyDto,
    @CurrentEmployee() employee: JWTPayload
  ): Promise<ApiResponseDto<{ id: string; avatarUploadUrl: string | null; }>> {
    const result = await this.agentStrategyService.updateStrategy(id, updateDto, Number(employee.id));
    return ApiResponseDto.success(result, 'Cập nhật chiến lược agent thành công');
  }

  /**
   * Xóa mềm chiến lược agent
   */
  @Delete(':id')
  @ApiOperation({
    summary: 'Xóa mềm chiến lược agent',
    description: 'API để xóa mềm một chiến lược agent (có thể khôi phục sau này)'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của chiến lược agent',
    example: 'agent-uuid-123'
  })
  @ApiResponse({
    status: 200,
    description: 'Xóa chiến lược agent thành công',
    schema: ApiResponseDto.getSchema(null)
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.STRATEGY_NOT_FOUND,
    AGENT_ERROR_CODES.STRATEGY_IN_USE,
    AGENT_ERROR_CODES.STRATEGY_DELETE_FAILED
  )
  async deleteStrategy(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentEmployee() employee: JWTPayload
  ): Promise<ApiResponseDto<null>> {
    await this.agentStrategyService.deleteStrategy(id, Number(employee.id));
    return ApiResponseDto.success(null, 'Xóa chiến lược agent thành công');
  }

  /**
   * Khôi phục chiến lược agent đã xóa
   */
  @Put(':id/restore')
  @ApiOperation({
    summary: 'Khôi phục chiến lược agent đã xóa',
    description: 'API để khôi phục một chiến lược agent đã bị xóa mềm'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của chiến lược agent',
    example: 'agent-uuid-123'
  })
  @ApiResponse({
    status: 200,
    description: 'Khôi phục chiến lược agent thành công',
    schema: ApiResponseDto.getSchema(null)
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.STRATEGY_NOT_FOUND,
    AGENT_ERROR_CODES.STRATEGY_RESTORE_FAILED
  )
  async restoreStrategy(
    @Param('id', ParseUUIDPipe) id: string
  ): Promise<ApiResponseDto<null>> {
    await this.agentStrategyService.restoreStrategy(id);
    return ApiResponseDto.success(null, 'Khôi phục chiến lược agent thành công');
  }
}