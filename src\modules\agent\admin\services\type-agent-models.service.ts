import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { TypeAgentModelsRepository } from '@modules/agent/repositories/type-agent-models.repository';
import { TypeAgentRepository } from '@modules/agent/repositories/type-agent.repository';
import { TypeAgentModelsResponseDto } from '../dto/type-agent/type-agent-models.dto';
import { Transactional } from 'typeorm-transactional';

/**
 * Service xử lý logic nghiệp vụ cho Type Agent Models
 */
@Injectable()
export class TypeAgentModelsService {
  private readonly logger = new Logger(TypeAgentModelsService.name);

  constructor(
    private readonly typeAgentModelsRepository: TypeAgentModelsRepository,
    private readonly typeAgentRepository: TypeAgentRepository,
  ) {}

  /**
   * L<PERSON>y danh sách models của type agent
   * @param typeAgentId ID của type agent
   * @returns Danh sách models
   */
  async getModelsByTypeAgent(typeAgentId: number): Promise<TypeAgentModelsResponseDto> {
    try {
      this.logger.log(`Getting models for type agent: ${typeAgentId}`);

      // Validate type agent exists
      await this.validateTypeAgentExists(typeAgentId);

      // Lấy danh sách model registry IDs
      const modelRegistryIds = await this.typeAgentModelsRepository.getModelRegistryIdsByTypeAgent(typeAgentId);

      return {
        typeAgentId,
        modelRegistryIds,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error getting models for type agent ${typeAgentId}: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_FETCH_FAILED);
    }
  }

  /**
   * Thêm models vào type agent
   * @param typeAgentId ID của type agent
   * @param modelRegistryIds Danh sách model registry IDs
   */
  @Transactional()
  async addModelsToTypeAgent(typeAgentId: number, modelRegistryIds: string[]): Promise<void> {
    try {
      this.logger.log(`Adding models to type agent ${typeAgentId}: ${modelRegistryIds.join(', ')}`);

      // Validate type agent exists
      await this.validateTypeAgentExists(typeAgentId);

      // Validate models exist (có thể thêm validation nếu cần)
      // await this.validateModelsExist(modelRegistryIds);

      // Thêm từng model vào type agent
      for (const modelRegistryId of modelRegistryIds) {
        await this.typeAgentModelsRepository.addModelToTypeAgent(typeAgentId, modelRegistryId);
      }

      this.logger.log(`Successfully added ${modelRegistryIds.length} models to type agent ${typeAgentId}`);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error adding models to type agent ${typeAgentId}: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Xóa models khỏi type agent
   * @param typeAgentId ID của type agent
   * @param modelRegistryIds Danh sách model registry IDs
   */
  @Transactional()
  async removeModelsFromTypeAgent(typeAgentId: number, modelRegistryIds: string[]): Promise<void> {
    try {
      this.logger.log(`Removing models from type agent ${typeAgentId}: ${modelRegistryIds.join(', ')}`);

      // Validate type agent exists
      await this.validateTypeAgentExists(typeAgentId);

      // Xóa từng model khỏi type agent
      for (const modelRegistryId of modelRegistryIds) {
        await this.typeAgentModelsRepository.removeModelFromTypeAgent(typeAgentId, modelRegistryId);
      }

      this.logger.log(`Successfully removed ${modelRegistryIds.length} models from type agent ${typeAgentId}`);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error removing models from type agent ${typeAgentId}: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Thay thế tất cả models của type agent
   * @param typeAgentId ID của type agent
   * @param modelRegistryIds Danh sách model registry IDs mới
   */
  @Transactional()
  async replaceModelsForTypeAgent(typeAgentId: number, modelRegistryIds: string[]): Promise<void> {
    try {
      this.logger.log(`Replacing models for type agent ${typeAgentId} with: ${modelRegistryIds.join(', ')}`);

      // Validate type agent exists
      await this.validateTypeAgentExists(typeAgentId);

      // Validate models exist (có thể thêm validation nếu cần)
      // await this.validateModelsExist(modelRegistryIds);

      // Thay thế tất cả models
      await this.typeAgentModelsRepository.replaceModelsForTypeAgent(typeAgentId, modelRegistryIds);

      this.logger.log(`Successfully replaced models for type agent ${typeAgentId} with ${modelRegistryIds.length} models`);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error replacing models for type agent ${typeAgentId}: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Xóa tất cả models khỏi type agent
   * @param typeAgentId ID của type agent
   */
  @Transactional()
  async removeAllModelsFromTypeAgent(typeAgentId: number): Promise<void> {
    try {
      this.logger.log(`Removing all models from type agent ${typeAgentId}`);

      // Validate type agent exists
      await this.validateTypeAgentExists(typeAgentId);

      // Xóa tất cả models
      await this.typeAgentModelsRepository.removeAllModelsFromTypeAgent(typeAgentId);

      this.logger.log(`Successfully removed all models from type agent ${typeAgentId}`);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error removing all models from type agent ${typeAgentId}: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Validate type agent exists
   * @param typeAgentId ID của type agent
   */
  private async validateTypeAgentExists(typeAgentId: number): Promise<void> {
    const typeAgent = await this.typeAgentRepository.findOne({
      where: { id: typeAgentId }
    });

    if (!typeAgent) {
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
    }
  }

  /**
   * Validate models exist (có thể implement sau nếu cần)
   * @param modelRegistryIds Danh sách model registry IDs
   */
  // private async validateModelsExist(modelRegistryIds: string[]): Promise<void> {
  //   // Implementation tùy thuộc vào cách validate model registry
  //   // Có thể cần inject ModelRegistryRepository
  // }
}
