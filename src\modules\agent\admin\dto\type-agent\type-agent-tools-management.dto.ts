import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsUUID } from 'class-validator';

/**
 * DTO cho việc thêm tools vào type agent
 */
export class AddToolsToTypeAgentDto {
  /**
   * Danh sách tool IDs cần thêm
   */
  @ApiProperty({
    description: 'Danh sách tool IDs cần thêm vào type agent',
    type: [String],
    example: ['550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'],
  })
  @IsArray()
  @IsUUID(4, { each: true, message: 'Mỗi toolId phải là UUID hợp lệ' })
  toolIds: string[];
}

/**
 * DTO cho việc xóa tools khỏi type agent
 */
export class RemoveToolsFromTypeAgentDto {
  /**
   * Danh sách tool IDs cần xóa
   */
  @ApiProperty({
    description: '<PERSON>h sách tool IDs cần xóa khỏi type agent',
    type: [String],
    example: ['550e8400-e29b-41d4-a716-************'],
  })
  @IsArray()
  @IsUUID(4, { each: true, message: 'Mỗi toolId phải là UUID hợp lệ' })
  toolIds: string[];
}

/**
 * DTO cho response danh sách tools của type agent
 */
export class TypeAgentToolsResponseDto {
  /**
   * ID của type agent
   */
  @ApiProperty({
    description: 'ID của type agent',
    example: 1,
  })
  typeAgentId: number;

  /**
   * Danh sách tool IDs
   */
  @ApiProperty({
    description: 'Danh sách tool IDs được gán cho type agent',
    type: [String],
    example: ['550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'],
  })
  toolIds: string[];
}
