import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsUUID, IsOptional, IsString, IsInt, Min } from 'class-validator';
import { Transform } from 'class-transformer';

/**
 * DTO cho việc thêm tools vào type agent
 */
export class AddToolsToTypeAgentDto {
  /**
   * Danh sách tool IDs cần thêm
   */
  @ApiProperty({
    description: 'Danh sách tool IDs cần thêm vào type agent',
    type: [String],
    example: ['550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'],
  })
  @IsArray()
  @IsUUID(4, { each: true, message: 'Mỗi toolId phải là UUID hợp lệ' })
  toolIds: string[];
}

/**
 * DTO cho việc xóa tools khỏi type agent
 */
export class RemoveToolsFromTypeAgentDto {
  /**
   * Danh sách tool IDs cần xóa
   */
  @ApiProperty({
    description: 'Danh sách tool IDs cần xóa khỏi type agent',
    type: [String],
    example: ['550e8400-e29b-41d4-a716-************'],
  })
  @IsArray()
  @IsUUID(4, { each: true, message: 'Mỗi toolId phải là UUID hợp lệ' })
  toolIds: string[];
}

/**
 * DTO cho query parameters của tools listing
 */
export class TypeAgentToolsQueryDto {
  /**
   * Số trang (bắt đầu từ 1)
   */
  @ApiPropertyOptional({
    description: 'Số trang (bắt đầu từ 1)',
    example: 1,
    minimum: 1,
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsInt()
  @Min(1)
  page?: number = 1;

  /**
   * Số lượng items per page
   */
  @ApiPropertyOptional({
    description: 'Số lượng items per page',
    example: 10,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsInt()
  @Min(1)
  limit?: number = 10;

  /**
   * Tìm kiếm theo tên tool
   */
  @ApiPropertyOptional({
    description: 'Tìm kiếm theo tên tool',
    example: 'search tool',
  })
  @IsOptional()
  @IsString()
  search?: string;
}

/**
 * DTO cho thông tin chi tiết tool
 */
export class TypeAgentToolItemDto {
  /**
   * ID của tool
   */
  @ApiProperty({
    description: 'ID của tool',
    example: '550e8400-e29b-41d4-a716-************',
  })
  id: string;

  /**
   * Tên của tool
   */
  @ApiProperty({
    description: 'Tên của tool',
    example: 'Web Search Tool',
  })
  name: string;

  /**
   * Mô tả của tool
   */
  @ApiProperty({
    description: 'Mô tả của tool',
    example: 'Tool để tìm kiếm thông tin trên web',
    nullable: true,
  })
  description: string | null;

  /**
   * Trạng thái của tool
   */
  @ApiProperty({
    description: 'Trạng thái của tool',
    example: 'DRAFT',
    enum: ['DRAFT', 'APPROVED', 'DEPRECATED'],
  })
  status: string;

  /**
   * Loại quyền truy cập
   */
  @ApiProperty({
    description: 'Loại quyền truy cập của tool',
    example: 'PUBLIC',
    enum: ['PUBLIC', 'PRIVATE', 'RESTRICTED'],
  })
  accessType: string;

  /**
   * Có đang bán không
   */
  @ApiProperty({
    description: 'Tool có đang được bán không',
    example: false,
  })
  isForSale: boolean;

  /**
   * Ngày tạo
   */
  @ApiProperty({
    description: 'Ngày tạo tool (timestamp)',
    example: 1682506892000,
  })
  createdAt: number;
}
