import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsNotEmpty, ValidateNested, ArrayMinSize, ArrayMaxSize } from 'class-validator';
import { CreateModelRegistryDto } from './create-model-registry.dto';

/**
 * DTO cho việc tạo nhiều model registry cùng lúc
 */
export class CreateBulkModelRegistryDto {
  /**
   * Danh sách các model registry cần tạo
   */
  @ApiProperty({
    description: 'Danh sách các model registry cần tạo',
    type: [CreateModelRegistryDto],
    example: [
      {
        provider: 'OPENAI',
        modelNamePattern: 'gpt-4*',
        inputModalities: ['text', 'image'],
        outputModalities: ['text'],
        samplingParameters: ['temperature', 'top_p'],
        features: ['tool_call'],
        basePricing: { inputRate: 1, outputRate: 2 },
        fineTunePricing: { inputRate: 2, outputRate: 4 },
        trainingPricing: 0
      },
      {
        provider: 'ANTHROPIC',
        modelNamePattern: 'claude-3*',
        inputModalities: ['text'],
        outputModalities: ['text'],
        samplingParameters: ['temperature'],
        features: ['tool_call'],
        basePricing: { inputRate: 1.5, outputRate: 3 },
        fineTunePricing: { inputRate: 3, outputRate: 6 },
        trainingPricing: 0
      }
    ],
    minItems: 1,
    maxItems: 50,
  })
  @IsArray()
  @IsNotEmpty()
  @ArrayMinSize(1, { message: 'Phải có ít nhất 1 model registry để tạo' })
  @ArrayMaxSize(50, { message: 'Không thể tạo quá 50 model registry cùng lúc' })
  @ValidateNested({ each: true })
  @Type(() => CreateModelRegistryDto)
  registries: CreateModelRegistryDto[];
}
