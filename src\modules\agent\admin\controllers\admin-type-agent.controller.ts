import { ErrorCode } from '@/common';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import { AdminTypeAgentService } from '@modules/agent/admin/services';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions/agent-error.code';
import { CurrentEmployee } from '@modules/auth/decorators';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseBoolPipe,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiExtraModels,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiTags,
} from '@nestjs/swagger';
import {
  BulkRestoreTypeAgentDto,
  CreateTypeAgentDto,
  DeleteTypeAgentDto,
  RestoreTypeAgentDto,
  TypeAgentDetailDto,
  TypeAgentListItemDto,
  TypeAgentQueryDto,
  TypeAgentTrashItemDto,
  UpdateTypeAgentDto,
  AddAgentSystemsDto,
  RemoveAgentSystemsDto,
  TypeAgentSystemsQueryDto,
  TypeAgentSystemItemDto,
} from '../dto/type-agent';

/**
 * Controller xử lý các endpoint liên quan đến Type Agent cho Admin
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_TYPE_AGENT)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard)
@Controller('admin/type-agents')
@ApiExtraModels(
  TypeAgentListItemDto,
  TypeAgentDetailDto,
  TypeAgentSystemItemDto,
  ApiResponseDto,
  PaginatedResult,
)
export class AdminTypeAgentController {
  constructor(private readonly adminTypeAgentService: AdminTypeAgentService) { }

  /**
   * Lấy danh sách loại agent với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách loại agent với phân trang
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách loại agent',
    description: 'Lấy danh sách loại agent với phân trang và lọc',
  })
  @ApiOkResponse({
    description: 'Danh sách loại agent',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.TYPE_AGENT_QUERY_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async findAll(
    @Query() queryDto: TypeAgentQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<TypeAgentListItemDto>>> {
    const result = await this.adminTypeAgentService.findAll(queryDto);
    return ApiResponseDto.success(result);
  }

  /**
   * Lấy danh sách loại agent đã xóa mềm
   * @param queryDto Tham số truy vấn
   * @returns Danh sách loại agent đã xóa với phân trang
   */
  @Get('trash')
  @ApiOperation({
    summary: 'Lấy danh sách loại agent đã xóa',
    description: 'Lấy danh sách loại agent đã xóa mềm với phân trang và tìm kiếm',
  })
  @ApiOkResponse({
    description: 'Danh sách loại agent đã xóa',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.TYPE_AGENT_QUERY_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async findAllDeleted(
    @Query() queryDto: TypeAgentQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<TypeAgentTrashItemDto>>> {
    const result = await this.adminTypeAgentService.findAllDeleted(queryDto);
    return ApiResponseDto.paginated(result);
  }

  /**
   * Lấy thông tin chi tiết loại agent theo ID
   * @param id ID của loại agent
   * @returns Thông tin chi tiết loại agent
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Lấy chi tiết loại agent',
    description: 'Lấy thông tin chi tiết loại agent theo ID',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của loại agent',
    type: Number,
  })
  @ApiOkResponse({
    description: 'Thông tin chi tiết loại agent',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.TYPE_AGENT_FETCH_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async findOne(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<TypeAgentDetailDto>> {
    const result = await this.adminTypeAgentService.findById(id);
    return ApiResponseDto.success(result);
  }

  /**
   * Tạo loại agent mới
   * @param createDto Dữ liệu tạo loại agent
   * @param employeeId ID của nhân viên tạo
   * @returns ID của loại agent đã tạo
   */
  @Post()
  @ApiOperation({
    summary: 'Tạo loại agent mới',
    description: 'Tạo loại agent mới với thông tin cung cấp. Có thể bao gồm tools và models ngay khi tạo.',
    requestBody: {
      description: 'Dữ liệu tạo type agent',
      content: {
        'application/json': {
          examples: {
            basicTypeAgent: {
              summary: 'Tạo type agent cơ bản',
              value: {
                name: 'Chatbot Agent',
                description: 'Loại agent hỗ trợ chat với người dùng',
                defaultConfig: {
                  enableAgentProfileCustomization: true,
                  enableOutputToMessenger: true,
                  enableOutputToWebsiteLiveChat: true,
                  enableTaskConversionTracking: false,
                  enableResourceUsage: true,
                  enableDynamicStrategyExecution: true,
                  enableMultiAgentCollaboration: false,
                  enableOutputToZaloOA: true,
                },
                status: 'DRAFT',
                agentSystems: ['agent-system-uuid-1', 'agent-system-uuid-2']
              }
            },
            typeAgentWithToolsAndModels: {
              summary: 'Tạo type agent với tools và models',
              value: {
                name: 'AI Assistant Agent',
                description: 'Loại agent AI với tools và models tích hợp',
                defaultConfig: {
                  enableAgentProfileCustomization: true,
                  enableOutputToMessenger: true,
                  enableOutputToWebsiteLiveChat: true,
                  enableTaskConversionTracking: false,
                  enableResourceUsage: true,
                  enableDynamicStrategyExecution: true,
                  enableMultiAgentCollaboration: true,
                  enableOutputToZaloOA: true,
                },
                status: 'DRAFT',
                agentSystems: ['agent-system-uuid-1'],
                toolIds: ['550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'],
                modelRegistryIds: ['550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************']
              }
            }
          }
        }
      }
    }
  })
  @ApiCreatedResponse({
    description: 'Loại agent đã được tạo thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.TYPE_AGENT_NAME_EXISTS,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async create(
    @Body() createDto: CreateTypeAgentDto,
    @CurrentEmployee('id') employeeId: number,
  ): Promise<ApiResponseDto<{id: number}>> {
    const result = await this.adminTypeAgentService.create(createDto, employeeId);
    return ApiResponseDto.success(result, 'Tạo loại agent thành công');
  }

  /**
   * Cập nhật thông tin loại agent
   * @param id ID của loại agent
   * @param updateDto Dữ liệu cập nhật
   * @param employeeId ID của nhân viên cập nhật
   */
  @Patch(':id')
  @ApiOperation({
    summary: 'Cập nhật loại agent',
    description: 'Cập nhật thông tin loại agent theo ID',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của loại agent',
    type: Number,
  })
  @ApiOkResponse({
    description: 'Loại agent đã được cập nhật thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.TYPE_AGENT_NAME_EXISTS,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateTypeAgentDto,
    @CurrentEmployee('id') employeeId: number,
  ): Promise<ApiResponseDto<{id: number}>> {
    const result = await this.adminTypeAgentService.update(id, updateDto, employeeId);
    return ApiResponseDto.success(result, 'Cập nhật loại agent thành công');
  }

  /**
   * Cập nhật trạng thái loại agent
   * @param id ID của loại agent
   * @param draft Query param để chuyển đổi DRAFT/APPROVED
   * @param employeeId ID của nhân viên cập nhật
   */
  @Patch(':id/status')
  @ApiOperation({
    summary: 'Cập nhật trạng thái loại agent',
    description: 'Cập nhật trạng thái loại agent theo ID với query param draft=true/false',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của loại agent',
    type: Number,
  })
  @ApiQuery({
    name: 'draft',
    description: 'true để chuyển về DRAFT, false để chuyển về APPROVED',
    type: Boolean,
    example: false,
  })
  @ApiOkResponse({
    description: 'Trạng thái loại agent đã được cập nhật thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.TYPE_AGENT_STATUS_UPDATE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async updateStatus(
    @Param('id', ParseIntPipe) id: number,
    @Query('draft', ParseBoolPipe) draft: boolean,
    @CurrentEmployee('id') employeeId: number,
  ): Promise<ApiResponseDto<null>> {
    await this.adminTypeAgentService.updateStatusByQuery(id, draft, employeeId);
    return ApiResponseDto.success(null, 'Cập nhật trạng thái loại agent thành công');
  }

  /**
   * Xóa loại agent (soft delete) với migration agents
   * @param id ID của loại agent
   * @param deleteDto Dữ liệu xóa với newTypeAgentId
   * @param employeeId ID của nhân viên xóa
   */
  @Delete(':id')
  @ApiOperation({
    summary: 'Xóa loại agent với migration',
    description: 'Xóa loại agent theo ID (soft delete) và chuyển các agents sang type mới',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của loại agent',
    type: Number,
  })
  @ApiOkResponse({
    description: 'Loại agent đã được xóa thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.TYPE_AGENT_ALREADY_DELETED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async remove(
    @Param('id', ParseIntPipe) id: number,
    @Body() deleteDto: DeleteTypeAgentDto,
    @CurrentEmployee('id') employeeId: number,
  ): Promise<ApiResponseDto<{ migratedAgents: number }>> {
    const migratedAgents = await this.adminTypeAgentService.removeWithMigration(id, deleteDto.newTypeAgentId, employeeId);
    return ApiResponseDto.success({ migratedAgents }, 'Xóa loại agent thành công');
  }

  /**
   * Khôi phục loại agent đã xóa mềm
   * @returns Kết quả khôi phục
   * @param id
   * @param employeeId
   */
  @Post(':id/restore')
  @ApiOperation({
    summary: 'Khôi phục loại agent đã xóa',
    description: 'Khôi phục loại agent đã xóa mềm về trạng thái bình thường',
  })
  @ApiCreatedResponse({
    description: 'Loại agent đã được khôi phục thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async restore(
    @Param('id', ParseIntPipe) id: number,
    @CurrentEmployee('id') employeeId: number,
  ): Promise<ApiResponseDto<null>> {
    await this.adminTypeAgentService.restore(id, employeeId);
    return ApiResponseDto.success(null, 'Khôi phục loại agent thành công');
  }

  /**
   * Khôi phục nhiều loại agent đã xóa mềm (bulk restore)
   * @param restoreDto Dữ liệu khôi phục nhiều
   * @param employeeId ID của nhân viên thực hiện khôi phục
   * @returns Kết quả khôi phục
   */
  @Post('restore')
  @ApiOperation({
    summary: 'Khôi phục nhiều loại agent đã xóa',
    description: 'Khôi phục nhiều loại agent đã xóa mềm về trạng thái bình thường',
  })
  @ApiCreatedResponse({
    description: 'Các loại agent đã được khôi phục thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async restoreBulk(
    @Body() restoreDto: RestoreTypeAgentDto,
    @CurrentEmployee('id') employeeId: number,
  ): Promise<ApiResponseDto<null>> {
    await this.adminTypeAgentService.restoreBulk(restoreDto.ids, employeeId);
    return ApiResponseDto.success(null, 'Khôi phục nhiều loại agent thành công');
  }

  /**
   * Khôi phục nhiều loại agent đã xóa mềm (legacy bulk restore)
   * @param bulkRestoreDto Dữ liệu khôi phục nhiều
   * @returns Kết quả khôi phục
   */
  @Post('restore/bulk')
  @ApiOperation({
    summary: 'Khôi phục nhiều loại agent đã xóa (legacy)',
    description: 'Khôi phục nhiều loại agent đã xóa mềm về trạng thái bình thường (legacy endpoint)',
  })
  @ApiCreatedResponse({
    description: 'Các loại agent đã được khôi phục thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async bulkRestore(
    @Body() bulkRestoreDto: BulkRestoreTypeAgentDto,
  ): Promise<ApiResponseDto<{ restoredCount: number }>> {
    const restoredCount = await this.adminTypeAgentService.bulkRestore(bulkRestoreDto.ids);
    return ApiResponseDto.success({ restoredCount }, 'Khôi phục nhiều loại agent thành công');
  }

  // ===== AGENT SYSTEMS MANAGEMENT ENDPOINTS =====

  /**
   * Lấy danh sách agent systems của type agent
   * @param id ID của type agent
   * @param queryDto Tham số truy vấn
   * @returns Danh sách agent systems với phân trang
   */
  @Get(':id/agent-systems')
  @ApiOperation({
    summary: 'Lấy danh sách agent systems của type agent',
    description: 'Lấy danh sách agent systems được gán cho type agent với phân trang và tìm kiếm',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của type agent',
    type: Number,
  })
  @ApiOkResponse({
    description: 'Danh sách agent systems của type agent',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.TYPE_AGENT_FETCH_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async getAgentSystems(
    @Param('id', ParseIntPipe) id: number,
    @Query() queryDto: TypeAgentSystemsQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<TypeAgentSystemItemDto>>> {
    const result = await this.adminTypeAgentService.getAgentSystems(id, queryDto);
    return ApiResponseDto.success(result);
  }

  /**
   * Thêm agent systems vào type agent
   * @param id ID của type agent
   * @param addDto Dữ liệu agent systems cần thêm
   * @param employeeId ID của nhân viên thực hiện
   * @returns Số lượng agent systems đã thêm
   */
  @Post(':id/agent-systems')
  @ApiOperation({
    summary: 'Thêm agent systems vào type agent',
    description: 'Thêm một hoặc nhiều agent systems vào type agent',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của type agent',
    type: Number,
  })
  @ApiCreatedResponse({
    description: 'Agent systems đã được thêm thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND,
    AGENT_ERROR_CODES.AGENT_SYSTEM_SUPERVISOR_NOT_ALLOWED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async addAgentSystems(
    @Param('id', ParseIntPipe) id: number,
    @Body() addDto: AddAgentSystemsDto,
    @CurrentEmployee('id') employeeId: number,
  ): Promise<ApiResponseDto<{ added: number }>> {
    const result = await this.adminTypeAgentService.addAgentSystems(id, addDto, employeeId);
    return ApiResponseDto.success(result, 'Thêm agent systems thành công');
  }

  /**
   * Gỡ agent systems khỏi type agent
   * @param id ID của type agent
   * @param removeDto Dữ liệu agent systems cần gỡ
   * @param employeeId ID của nhân viên thực hiện
   * @returns Số lượng agent systems đã gỡ
   */
  @Delete(':id/agent-systems')
  @ApiOperation({
    summary: 'Gỡ agent systems khỏi type agent',
    description: 'Gỡ một hoặc nhiều agent systems khỏi type agent',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của type agent',
    type: Number,
  })
  @ApiOkResponse({
    description: 'Agent systems đã được gỡ thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.TYPE_AGENT_UPDATE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async removeAgentSystems(
    @Param('id', ParseIntPipe) id: number,
    @Body() removeDto: RemoveAgentSystemsDto,
    @CurrentEmployee('id') employeeId: number,
  ): Promise<ApiResponseDto<{ removed: number }>> {
    const result = await this.adminTypeAgentService.removeAgentSystems(id, removeDto, employeeId);
    return ApiResponseDto.success(result, 'Gỡ agent systems thành công');
  }

  // ===== TOOLS MANAGEMENT ENDPOINTS =====

  /**
   * Lấy danh sách tools của type agent
   */
  @Get(':id/tools')
  @ApiOperation({
    summary: 'Lấy danh sách tools của type agent',
    description: 'API để lấy danh sách tất cả tools được gán cho một type agent cụ thể'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của type agent',
    example: 1,
    type: 'integer'
  })
  @ApiOkResponse({
    description: 'Lấy danh sách tools thành công',
    type: ApiResponseDto
  })
  async getTools(
    @Param('id', ParseIntPipe) id: number
  ): Promise<ApiResponseDto<{ typeAgentId: number; toolIds: string[] }>> {
    const result = await this.adminTypeAgentService.getTools(id);
    return ApiResponseDto.success(result, 'Lấy danh sách tools thành công');
  }

  /**
   * Thêm tools vào type agent
   */
  @Post(':id/tools')
  @ApiOperation({
    summary: 'Thêm tools vào type agent',
    description: 'API để thêm một hoặc nhiều tools vào type agent'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của type agent',
    example: 1,
    type: 'integer'
  })
  @ApiCreatedResponse({
    description: 'Thêm tools thành công',
    type: ApiResponseDto
  })
  async addTools(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: { toolIds: string[] }
  ): Promise<ApiResponseDto<null>> {
    await this.adminTypeAgentService.addTools(id, body.toolIds);
    return ApiResponseDto.created(null, 'Thêm tools vào type agent thành công');
  }

  /**
   * Xóa tools khỏi type agent
   */
  @Delete(':id/tools')
  @ApiOperation({
    summary: 'Xóa tools khỏi type agent',
    description: 'API để xóa một hoặc nhiều tools khỏi type agent'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của type agent',
    example: 1,
    type: 'integer'
  })
  @ApiOkResponse({
    description: 'Xóa tools thành công',
    type: ApiResponseDto
  })
  async removeTools(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: { toolIds: string[] }
  ): Promise<ApiResponseDto<null>> {
    await this.adminTypeAgentService.removeTools(id, body.toolIds);
    return ApiResponseDto.success(null, 'Xóa tools khỏi type agent thành công');
  }

  // ===== MODELS MANAGEMENT ENDPOINTS =====

  /**
   * Lấy danh sách models của type agent
   */
  @Get(':id/models')
  @ApiOperation({
    summary: 'Lấy danh sách models của type agent',
    description: 'API để lấy danh sách tất cả model registry được gán cho một type agent cụ thể'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của type agent',
    example: 1,
    type: 'integer'
  })
  @ApiOkResponse({
    description: 'Lấy danh sách models thành công',
    type: ApiResponseDto
  })
  async getModels(
    @Param('id', ParseIntPipe) id: number
  ): Promise<ApiResponseDto<{ typeAgentId: number; modelRegistryIds: string[] }>> {
    const result = await this.adminTypeAgentService.getModels(id);
    return ApiResponseDto.success(result, 'Lấy danh sách models thành công');
  }

  /**
   * Thay thế tất cả models của type agent
   */
  @Post(':id/models')
  @ApiOperation({
    summary: 'Thay thế tất cả models của type agent',
    description: 'API để thay thế toàn bộ danh sách model registry của type agent'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của type agent',
    example: 1,
    type: 'integer'
  })
  @ApiCreatedResponse({
    description: 'Thay thế models thành công',
    type: ApiResponseDto
  })
  async replaceModels(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: { modelRegistryIds: string[] }
  ): Promise<ApiResponseDto<null>> {
    await this.adminTypeAgentService.replaceModels(id, body.modelRegistryIds);
    return ApiResponseDto.created(null, 'Thay thế models cho type agent thành công');
  }

  /**
   * Xóa models khỏi type agent
   */
  @Delete(':id/models')
  @ApiOperation({
    summary: 'Xóa models khỏi type agent',
    description: 'API để xóa một hoặc nhiều model registry khỏi type agent'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của type agent',
    example: 1,
    type: 'integer'
  })
  @ApiOkResponse({
    description: 'Xóa models thành công',
    type: ApiResponseDto
  })
  async removeModels(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: { modelRegistryIds: string[] }
  ): Promise<ApiResponseDto<null>> {
    await this.adminTypeAgentService.removeModels(id, body.modelRegistryIds);
    return ApiResponseDto.success(null, 'Xóa models khỏi type agent thành công');
  }
}
