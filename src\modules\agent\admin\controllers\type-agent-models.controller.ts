import { Controller, Get, Post, Delete, Put, Body, Param, ParseIntPipe } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBody } from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards/jwt-employee.guard';
import { UseGuards } from '@nestjs/common';
import { ApiBearerAuth } from '@nestjs/swagger';
import { ApiResponseDto } from '@common/response';
import { TypeAgentModelsService } from '../services/type-agent-models.service';
import { 
  AddModelsToTypeAgentDto, 
  RemoveModelsFromTypeAgentDto, 
  ReplaceModelsForTypeAgentDto,
  TypeAgentModelsResponseDto 
} from '../dto/type-agent/type-agent-models.dto';

/**
 * Controller quản lý models cho type agent
 */
@ApiTags('Admin - Type Agent Models')
@Controller('admin/type-agent-models')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
export class TypeAgentModelsController {
  constructor(private readonly typeAgentModelsService: TypeAgentModelsService) {}

  /**
   * Lấy danh sách models của type agent
   */
  @Get(':typeAgentId')
  @ApiOperation({
    summary: 'Lấy danh sách models của type agent',
    description: 'API để lấy danh sách tất cả model registry được gán cho một type agent cụ thể'
  })
  @ApiParam({
    name: 'typeAgentId',
    description: 'ID của type agent',
    example: 1,
    type: 'integer'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách models thành công',
    schema: ApiResponseDto.getSchema(TypeAgentModelsResponseDto)
  })
  async getModelsByTypeAgent(
    @Param('typeAgentId', ParseIntPipe) typeAgentId: number
  ): Promise<ApiResponseDto<TypeAgentModelsResponseDto>> {
    const result = await this.typeAgentModelsService.getModelsByTypeAgent(typeAgentId);
    return ApiResponseDto.success(result, 'Lấy danh sách models thành công');
  }

  /**
   * Thêm models vào type agent
   */
  @Post('add')
  @ApiOperation({
    summary: 'Thêm models vào type agent',
    description: 'API để thêm một hoặc nhiều model registry vào type agent'
  })
  @ApiBody({
    description: 'Dữ liệu thêm models',
    type: AddModelsToTypeAgentDto,
    examples: {
      addSingleModel: {
        summary: 'Thêm một model',
        value: {
          typeAgentId: 1,
          modelRegistryIds: ['550e8400-e29b-41d4-a716-446655440000']
        }
      },
      addMultipleModels: {
        summary: 'Thêm nhiều models',
        value: {
          typeAgentId: 1,
          modelRegistryIds: [
            '550e8400-e29b-41d4-a716-446655440000',
            '550e8400-e29b-41d4-a716-446655440001'
          ]
        }
      }
    }
  })
  @ApiResponse({
    status: 201,
    description: 'Thêm models thành công',
    schema: ApiResponseDto.getSchema(null)
  })
  async addModelsToTypeAgent(
    @Body() addDto: AddModelsToTypeAgentDto
  ): Promise<ApiResponseDto<null>> {
    await this.typeAgentModelsService.addModelsToTypeAgent(addDto.typeAgentId, addDto.modelRegistryIds);
    return ApiResponseDto.created(null, 'Thêm models vào type agent thành công');
  }

  /**
   * Xóa models khỏi type agent
   */
  @Delete('remove')
  @ApiOperation({
    summary: 'Xóa models khỏi type agent',
    description: 'API để xóa một hoặc nhiều model registry khỏi type agent'
  })
  @ApiBody({
    description: 'Dữ liệu xóa models',
    type: RemoveModelsFromTypeAgentDto,
    examples: {
      removeSingleModel: {
        summary: 'Xóa một model',
        value: {
          typeAgentId: 1,
          modelRegistryIds: ['550e8400-e29b-41d4-a716-446655440000']
        }
      },
      removeMultipleModels: {
        summary: 'Xóa nhiều models',
        value: {
          typeAgentId: 1,
          modelRegistryIds: [
            '550e8400-e29b-41d4-a716-446655440000',
            '550e8400-e29b-41d4-a716-446655440001'
          ]
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: 'Xóa models thành công',
    schema: ApiResponseDto.getSchema(null)
  })
  async removeModelsFromTypeAgent(
    @Body() removeDto: RemoveModelsFromTypeAgentDto
  ): Promise<ApiResponseDto<null>> {
    await this.typeAgentModelsService.removeModelsFromTypeAgent(removeDto.typeAgentId, removeDto.modelRegistryIds);
    return ApiResponseDto.success(null, 'Xóa models khỏi type agent thành công');
  }

  /**
   * Thay thế tất cả models của type agent
   */
  @Put('replace')
  @ApiOperation({
    summary: 'Thay thế tất cả models của type agent',
    description: 'API để thay thế toàn bộ danh sách model registry của type agent'
  })
  @ApiBody({
    description: 'Dữ liệu thay thế models',
    type: ReplaceModelsForTypeAgentDto,
    examples: {
      replaceWithNewModels: {
        summary: 'Thay thế bằng models mới',
        value: {
          typeAgentId: 1,
          modelRegistryIds: [
            '550e8400-e29b-41d4-a716-446655440000',
            '550e8400-e29b-41d4-a716-446655440001'
          ]
        }
      },
      replaceWithEmptyList: {
        summary: 'Xóa tất cả models',
        value: {
          typeAgentId: 1,
          modelRegistryIds: []
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: 'Thay thế models thành công',
    schema: ApiResponseDto.getSchema(null)
  })
  async replaceModelsForTypeAgent(
    @Body() replaceDto: ReplaceModelsForTypeAgentDto
  ): Promise<ApiResponseDto<null>> {
    await this.typeAgentModelsService.replaceModelsForTypeAgent(replaceDto.typeAgentId, replaceDto.modelRegistryIds);
    return ApiResponseDto.success(null, 'Thay thế models cho type agent thành công');
  }

  /**
   * Xóa tất cả models khỏi type agent
   */
  @Delete(':typeAgentId/all')
  @ApiOperation({
    summary: 'Xóa tất cả models khỏi type agent',
    description: 'API để xóa tất cả model registry khỏi một type agent cụ thể'
  })
  @ApiParam({
    name: 'typeAgentId',
    description: 'ID của type agent',
    example: 1,
    type: 'integer'
  })
  @ApiResponse({
    status: 200,
    description: 'Xóa tất cả models thành công',
    schema: ApiResponseDto.getSchema(null)
  })
  async removeAllModelsFromTypeAgent(
    @Param('typeAgentId', ParseIntPipe) typeAgentId: number
  ): Promise<ApiResponseDto<null>> {
    await this.typeAgentModelsService.removeAllModelsFromTypeAgent(typeAgentId);
    return ApiResponseDto.success(null, 'Xóa tất cả models khỏi type agent thành công');
  }
}
