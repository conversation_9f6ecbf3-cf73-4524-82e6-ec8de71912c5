import { ModelConfig } from '@/modules/agent/interfaces/model-config.interface';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsIn,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
  MaxLength,
  ValidateNested,
} from 'class-validator';
import { IsValidModelConfiguration, IsCompleteUserModelBlock } from '../../validators/model-validation.decorator';

/**
 * DTO cho thông tin profile của agent - Validation lỏng hơn
 */
export class ProfileModularDto {
  /**
   * Giới tính
   */
  @ApiPropertyOptional({
    description: 'Giới tính',
    example: 'MALE',
  })
  @IsString()
  @IsOptional()
  gender?: string;

  /**
   * <PERSON><PERSON>y sinh (timestamp millis)
   */
  @ApiPropertyOptional({
    description: '<PERSON><PERSON><PERSON> sinh (timestamp millis)',
    example: ************,
  })
  @IsNumber()
  @IsOptional()
  dateOfBirth?: number;

  /**
   * Vị trí
   */
  @ApiPropertyOptional({
    description: 'Vị trí',
    example: 'Trợ lý AI',
  })
  @IsString()
  @IsOptional()
  position?: string;

  /**
   * Học vấn
   */
  @ApiPropertyOptional({
    description: 'Học vấn',
    example: 'Đại học',
  })
  @IsString()
  @IsOptional()
  education?: string;

  /**
   * Kỹ năng
   */
  @ApiPropertyOptional({
    description: 'Kỹ năng',
    example: ['Trả lời câu hỏi', 'Tìm kiếm thông tin'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  skills?: string[];

  /**
   * Tính cách
   */
  @ApiPropertyOptional({
    description: 'Tính cách',
    example: ['Thân thiện', 'Kiên nhẫn'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  personality?: string[];

  /**
   * Ngôn ngữ
   */
  @ApiPropertyOptional({
    description: 'Ngôn ngữ',
    example: ['Tiếng Việt', 'Tiếng Anh'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  languages?: string[];

  /**
   * Quốc gia
   */
  @ApiPropertyOptional({
    description: 'Quốc gia',
    example: 'Việt Nam',
  })
  @IsString()
  @IsOptional()
  nations?: string;
}

/**
 * DTO cho khối Output
 */
export class OutputMessengerBlockDto {
  /**
   * Danh sách ID của Facebook Pages
   */
  @ApiPropertyOptional({
    description: 'Danh sách ID của Facebook Pages',
    example: ['page-uuid-1', 'page-uuid-2'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  facebookPageIds?: string[];
}

export class OutputWebsiteBlockDto {
  /**
   * Danh sách ID của User Websites
   */
  @ApiPropertyOptional({
    description: 'Danh sách ID của User Websites',
    example: ['website-uuid-1', 'website-uuid-2'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  userWebsiteIds?: string[];
}

/**
 * DTO cho khối Resources
 */
export class ResourcesBlockDto {
  /**
   * Danh sách ID của URLs
   */
  @ApiPropertyOptional({
    description: 'Danh sách ID của URLs',
    example: ['url-uuid-1', 'url-uuid-2'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  urlIds?: string[];

  /**
   * Danh sách ID của Media files
   */
  @ApiPropertyOptional({
    description: 'Danh sách ID của Media files',
    example: ['media-uuid-1', 'media-uuid-2'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  mediaIds?: string[];

  /**
   * Danh sách ID của Products
   */
  @ApiPropertyOptional({
    description: 'Danh sách ID của Products',
    example: [1, 2, 3],
    type: [Number],
  })
  @IsArray()
  @IsNumber({}, { each: true })
  @IsOptional()
  productIds?: number[];
}

/**
 * DTO cho khối Strategy - Đơn giản hóa chỉ cần strategyId
 */
export class StrategyBlockDto {
  /**
   * ID của strategy
   */
  @ApiPropertyOptional({
    description: 'ID của strategy',
    example: 'strategy-uuid-1',
  })
  @IsUUID()
  @IsOptional()
  strategyId?: string;
}


export class ConversionBlockDto {
  @ApiProperty({
    description: 'Tên của field trong schema JSON',
    example: 'email',
  })
  @IsString()
  // @Matches(/^[a-zA-Z_][a-zA-Z0-9_]*$/, {
  //   message: 'Tên field chỉ chứa chữ, số và _ và không bắt đầu bằng số',
  // })
  name: string;

  @ApiProperty({
    description: 'Kiểu dữ liệu của field',
    example: 'string',
  })
  @IsString()
  @IsIn(['string', 'number', 'boolean', 'array', 'object'], {
    message: 'Kiểu dữ liệu không hợp lệ',
  })
  type: 'string' | 'number' | 'boolean' | 'array_number' | 'array_string' | 'enum';

  @ApiProperty({
    description: 'Mô tả (nội dung) của field',
    example: 'Địa chỉ email người dùng',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Trường này có bắt buộc không?',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  required?: boolean;

  @ApiProperty({
    description: 'Trường này có được kích hoạt trong schema không?',
    example: true,
    default: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  active?: boolean = true;
}

/**
 * DTO cho một agent trong multi agent
 */
export class MultiAgentItemDto {
  /**
   * ID của agent
   */
  @ApiProperty({
    description: 'ID của agent',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsNotEmpty({ message: 'ID của agent không được để trống' })
  @IsUUID('4', { message: 'ID của agent phải là UUID hợp lệ' })
  agent_id: string;

  /**
   * Prompt cho agent này
   */
  @ApiProperty({
    description: 'Prompt cho agent này',
    example: 'Bạn là trợ lý chuyên về marketing, hãy hỗ trợ tạo nội dung quảng cáo',
  })
  @IsNotEmpty({ message: 'Prompt không được để trống' })
  @IsString({ message: 'Prompt phải là chuỗi ký tự' })
  @MaxLength(2000, { message: 'Prompt không được vượt quá 2000 ký tự' })
  prompt: string;
}

/**
 * DTO cho khối Multi Agent
 */
export class MultiAgentBlockDto {
  /**
   * Danh sách các agent với prompt tương ứng
   */
  @ApiPropertyOptional({
    description: 'Danh sách các agent với prompt tương ứng',
    example: [
      {
        agent_id: '123e4567-e89b-12d3-a456-************',
        prompt: 'Bạn là trợ lý chuyên về marketing'
      },
      {
        agent_id: '123e4567-e89b-12d3-a456-************',
        prompt: 'Bạn là trợ lý chuyên về kỹ thuật'
      }
    ],
    type: [MultiAgentItemDto],
  })
  @IsArray({ message: 'Multi agent phải là một mảng' })
  @ValidateNested({ each: true })
  @Type(() => MultiAgentItemDto)
  @IsOptional()
  multiAgent?: MultiAgentItemDto[];
}

export class OutputZaloBlockDto {
  /**
   * Danh sách ID của Zalo Official Accounts
   */
  @ApiPropertyOptional({
    description: 'Danh sách ID của Zalo Official Accounts',
    example: [1, 2, 3],
    type: [Number],
  })
  @IsArray()
  @IsNumber({}, { each: true })
  @IsOptional()
  zaloOfficialAccountIds: number[];
}

export class ModelConfigDto implements ModelConfig {
  /**
   * Giá trị temperature cho model (0-2)
   */
  @ApiPropertyOptional({
    description: 'Giá trị temperature cho model (0-2)',
    example: 1.0,
  })
  @IsOptional()
  temperature?: number;

  /**
   * Giá trị top_p cho model (0-1)
   */
  @ApiPropertyOptional({
    description: 'Giá trị top_p cho model (0-1)',
    example: 1.0,
  })
  @IsOptional()
  top_p?: number;

  /**
   * Giá trị top_k cho model
   */
  @ApiPropertyOptional({
    description: 'Giá trị top_k cho model',
    example: 1.0,
  })
  @IsOptional()
  top_k?: number;


}

/**
 * DTO cho việc tạo agent mới với cấu trúc modular
 * Logic model: Bắt buộc có 1 trong 2 khối:
 * - Khối 1: systemModelId
 * - Khối 2: userModelId + keyLlmId
 */
export class CreateAgentDto {
  /**
   * Tên agent
   */
  @ApiProperty({
    description: 'Tên agent',
    example: 'My Assistant',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  /**
   * ID loại agent
   */
  @ApiProperty({
    description: 'ID loại agent',
    example: 1,
  })
  @IsNumber()
  typeId: number;

  /**
   * ID của model từ user model (khối 2 - bắt buộc cùng với keyLlmId nếu không có systemModelId)
   */
  @ApiPropertyOptional({
    description: 'ID của model từ user model (khối 2 - bắt buộc cùng với keyLlmId nếu không có systemModelId)',
    example: 'user-model-uuid',
  })
  @IsString()
  @IsOptional()
  @IsCompleteUserModelBlock()
  userModelId?: string;

  /**
   * ID của key LLM từ user key LLM (khối 2 - bắt buộc cùng với userModelId nếu không có systemModelId)
   */
  @ApiPropertyOptional({
    description: 'ID của key LLM từ user key LLM (khối 2 - bắt buộc cùng với userModelId nếu không có systemModelId)',
    example: 'key-llm-uuid',
  })
  @IsString()
  @IsOptional()
  keyLlmId?: string;

  /**
   * ID của model fine-tune (khối 2 - tùy chọn)
   */
  @ApiPropertyOptional({
    description: 'ID của model fine-tune (khối 3 - tùy chọn)',
    example: 'model-fine-tune-uuid',
  })
  @IsString()
  @IsOptional()
  modelFineTuneId?: string;

  /**
   * ID của model từ system model (khối 1 - bắt buộc nếu không có khối 2)
   */
  @ApiPropertyOptional({
    description: 'ID của model từ system model (khối 1 - bắt buộc nếu không có khối 2)',
    example: 'system-model-uuid',
  })
  @IsString()
  @IsOptional()
  @IsValidModelConfiguration()
  systemModelId?: string;

  /**
   * MIME type của avatar (có thể null)
   */
  @ApiPropertyOptional({
    description: 'MIME type của avatar',
    example: 'image/jpeg',
  })
  @IsString()
  @IsOptional()
  avatarMimeType?: string;

  /**
   * Cấu hình model
   */
  @ApiProperty({
    description: 'Cấu hình model',
    type: ModelConfigDto,
  })
  @ValidateNested()
  @Type(() => ModelConfigDto)
  modelConfig: ModelConfigDto;

  /**
   * Hướng dẫn (instruction) - có thể null
   */
  @ApiPropertyOptional({
    description: 'Hướng dẫn (instruction)',
    example: 'Bạn là trợ lý cá nhân, hãy giúp người dùng giải đáp các thắc mắc',
  })
  @IsString()
  @IsOptional()
  instruction?: string;

  /**
   * ID của vector store - có thể null
   */
  @ApiPropertyOptional({
    description: 'ID của vector store',
    example: 'vector-store-1',
  })
  @IsString()
  @IsOptional()
  vectorStoreId?: string;

  /**
   * Thông tin profile - có thể null
   */
  @ApiPropertyOptional({
    description: 'Thông tin profile',
    type: ProfileModularDto,
  })
  @ValidateNested()
  @Type(() => ProfileModularDto)
  @IsOptional()
  profile?: ProfileModularDto;

  /**
   * Cấu hình chuyển đổi - có thể null
   */
  @ApiPropertyOptional({
    description: 'Cấu hình chuyển đổi',
    type: [ConversionBlockDto],
  })
  @ValidateNested({ each: true })
  @Type(() => ConversionBlockDto)
  @IsOptional()
  conversion?: ConversionBlockDto[];

  /**
   * Khối Output - có thể null
   */
  @ApiPropertyOptional({
    description: 'Khối Output',
    type: OutputMessengerBlockDto,
  })
  @ValidateNested()
  @Type(() => OutputMessengerBlockDto)
  @IsOptional()
  outputMessenger?: OutputMessengerBlockDto;

  /**
   * Khối Output - có thể null
   */
  @ApiPropertyOptional({
    description: 'Khối Output',
    type: OutputWebsiteBlockDto,
  })
  @ValidateNested()
  @Type(() => OutputWebsiteBlockDto)
  @IsOptional()
  outputWebsite?: OutputWebsiteBlockDto;

  /**
   * Khối Resources - có thể null
   */
  @ApiPropertyOptional({
    description: 'Khối Resources',
    type: ResourcesBlockDto,
  })
  @ValidateNested()
  @Type(() => ResourcesBlockDto)
  @IsOptional()
  resources?: ResourcesBlockDto;

  /**
   * Khối Strategy - có thể null
   */
  @ApiPropertyOptional({
    description: 'Khối Strategy',
    type: StrategyBlockDto,
  })
  @ValidateNested()
  @Type(() => StrategyBlockDto)
  @IsOptional()
  strategy?: StrategyBlockDto;

  /**
   * Khối Multi Agent - có thể null
   */
  @ApiPropertyOptional({
    description: 'Khối Multi Agent',
    type: MultiAgentBlockDto,
  })
  @ValidateNested()
  @Type(() => MultiAgentBlockDto)
  @IsOptional()
  multiAgent?: MultiAgentBlockDto;

  /**
   * Danh sách ID của user custom tools
   */
  @ApiPropertyOptional({
    description: 'Danh sách ID của user custom tools',
    example: ['tool-uuid-1', 'tool-uuid-2'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  customToolIds?: string[];

  /**
   * Khối Output Zalo - có thể null
   */
  @ApiPropertyOptional({
    description: 'Khối Output Zalo',
    type: OutputZaloBlockDto,
  })
  @ValidateNested()
  @Type(() => OutputZaloBlockDto)
  @IsOptional()
  outputZalo?: OutputZaloBlockDto;
}
