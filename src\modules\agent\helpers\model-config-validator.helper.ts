import { SamplingParameterEnum } from '@modules/models/constants/model-capabilities.enum';
import { ModelConfig } from '@modules/agent/interfaces/model-config.interface';
import { AppException } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions/agent-error.code';

/**
 * Helper để validate và filter model config dựa trên SamplingParameterEnum
 */
export class ModelConfigValidatorHelper {
  /**
   * Validate và filter model config dựa trên các tham số sampling được model hỗ trợ
   * @param modelConfig Cấu hình model từ request
   * @param allowedSamplingParameters Danh sách tham số sampling được model hỗ trợ
   * @returns ModelConfig đã được filter và validate
   */
  static validateAndFilterModelConfig(
    modelConfig: ModelConfig,
    allowedSamplingParameters: SamplingParameterEnum[]
  ): ModelConfig {
    const filteredConfig: ModelConfig = {};

    // Validate và lưu temperature nếu model hỗ trợ
    if (modelConfig.temperature !== undefined) {
      if (!allowedSamplingParameters.includes(SamplingParameterEnum.TEMPERATURE)) {
        throw new AppException({
          errorCode: AGENT_ERROR_CODES.INVALID_MODEL_CONFIG,
          message: `Model không hỗ trợ tham số temperature`,
          details: {
            parameter: 'temperature',
            allowedParameters: allowedSamplingParameters,
          },
        });
      }
      
      // Validate giá trị temperature (0-2)
      if (modelConfig.temperature < 0 || modelConfig.temperature > 2) {
        throw new AppException({
          errorCode: AGENT_ERROR_CODES.INVALID_MODEL_CONFIG,
          message: `Giá trị temperature phải trong khoảng 0-2`,
          details: {
            parameter: 'temperature',
            value: modelConfig.temperature,
            validRange: '0-2',
          },
        });
      }
      
      filteredConfig.temperature = modelConfig.temperature;
    }

    // Validate và lưu top_p nếu model hỗ trợ
    if (modelConfig.top_p !== undefined) {
      if (!allowedSamplingParameters.includes(SamplingParameterEnum.TOP_P)) {
        throw new AppException({
          errorCode: AGENT_ERROR_CODES.INVALID_MODEL_CONFIG,
          message: `Model không hỗ trợ tham số top_p`,
          details: {
            parameter: 'top_p',
            allowedParameters: allowedSamplingParameters,
          },
        });
      }
      
      // Validate giá trị top_p (0-1)
      if (modelConfig.top_p < 0 || modelConfig.top_p > 1) {
        throw new AppException({
          errorCode: AGENT_ERROR_CODES.INVALID_MODEL_CONFIG,
          message: `Giá trị top_p phải trong khoảng 0-1`,
          details: {
            parameter: 'top_p',
            value: modelConfig.top_p,
            validRange: '0-1',
          },
        });
      }
      
      filteredConfig.top_p = modelConfig.top_p;
    }

    // Validate và lưu top_k nếu model hỗ trợ
    if (modelConfig.top_k !== undefined) {
      if (!allowedSamplingParameters.includes(SamplingParameterEnum.TOP_K)) {
        throw new AppException({
          errorCode: AGENT_ERROR_CODES.INVALID_MODEL_CONFIG,
          message: `Model không hỗ trợ tham số top_k`,
          details: {
            parameter: 'top_k',
            allowedParameters: allowedSamplingParameters,
          },
        });
      }
      
      // Validate giá trị top_k (>= 0)
      if (modelConfig.top_k < 0) {
        throw new AppException({
          errorCode: AGENT_ERROR_CODES.INVALID_MODEL_CONFIG,
          message: `Giá trị top_k phải >= 0`,
          details: {
            parameter: 'top_k',
            value: modelConfig.top_k,
            validRange: '>= 0',
          },
        });
      }
      
      filteredConfig.top_k = modelConfig.top_k;
    }

    // max_tokens luôn được chấp nhận (không phụ thuộc vào SamplingParameterEnum)
    if (modelConfig.max_tokens !== undefined) {
      // Validate giá trị max_tokens (>= 1)
      if (modelConfig.max_tokens < 1) {
        throw new AppException({
          errorCode: AGENT_ERROR_CODES.INVALID_MODEL_CONFIG,
          message: `Giá trị max_tokens phải >= 1`,
          details: {
            parameter: 'max_tokens',
            value: modelConfig.max_tokens,
            validRange: '>= 1',
          },
        });
      }
      
      filteredConfig.max_tokens = modelConfig.max_tokens;
    }

    return filteredConfig;
  }

  /**
   * Lấy danh sách tham số sampling được model hỗ trợ từ system model
   * @param systemModelId ID của system model
   * @returns Promise<SamplingParameterEnum[]>
   */
  static async getModelSamplingParameters(
    systemModelId: string,
    systemModelsRepository: any
  ): Promise<SamplingParameterEnum[]> {
    try {
      const systemModel = await systemModelsRepository.findById(systemModelId);
      
      if (!systemModel) {
        throw new AppException({
          errorCode: AGENT_ERROR_CODES.INVALID_MODEL_CONFIG,
          message: `System model không tồn tại`,
          details: { systemModelId },
        });
      }

      // Lấy sampling parameters từ model registry thông qua system model
      const modelRegistry = systemModel.modelRegistry;
      if (!modelRegistry || !modelRegistry.samplingParameters) {
        // Nếu không có thông tin, trả về mặc định
        return [SamplingParameterEnum.TEMPERATURE];
      }

      return modelRegistry.samplingParameters;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      
      throw new AppException({
        errorCode: AGENT_ERROR_CODES.INVALID_MODEL_CONFIG.errorCode,
        message: `Lỗi khi lấy thông tin model sampling parameters`,
        details: { systemModelId, error: error.message },
      });
    }
  }
}
