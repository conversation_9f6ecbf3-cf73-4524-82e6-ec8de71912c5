import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsUUID } from 'class-validator';

/**
 * DTO cho việc thay thế models của type agent
 */
export class ReplaceModelsForTypeAgentDto {
  /**
   * Danh sách model registry IDs mới (thay thế toàn bộ)
   */
  @ApiProperty({
    description: 'Danh sách model registry IDs mới để thay thế toàn bộ models của type agent',
    type: [String],
    example: ['550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'],
  })
  @IsArray()
  @IsUUID(4, { each: true, message: 'Mỗi modelRegistryId phải là UUID hợp lệ' })
  modelRegistryIds: string[];
}

/**
 * DTO cho việc xóa models khỏi type agent
 */
export class RemoveModelsFromTypeAgentDto {
  /**
   * Danh sách model registry IDs cần xóa
   */
  @ApiProperty({
    description: 'Danh sách model registry IDs cần xóa khỏi type agent',
    type: [String],
    example: ['550e8400-e29b-41d4-a716-************'],
  })
  @IsArray()
  @IsUUID(4, { each: true, message: 'Mỗi modelRegistryId phải là UUID hợp lệ' })
  modelRegistryIds: string[];
}

/**
 * DTO cho response danh sách models của type agent
 */
export class TypeAgentModelsResponseDto {
  /**
   * ID của type agent
   */
  @ApiProperty({
    description: 'ID của type agent',
    example: 1,
  })
  typeAgentId: number;

  /**
   * Danh sách model registry IDs
   */
  @ApiProperty({
    description: 'Danh sách model registry IDs được gán cho type agent',
    type: [String],
    example: ['550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'],
  })
  modelRegistryIds: string[];
}
