import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsUUID, IsOptional, IsString, IsInt, Min } from 'class-validator';
import { Transform } from 'class-transformer';
import { ProviderEnum } from '@/modules/models/constants';

/**
 * DTO cho việc thay thế models của type agent
 */
export class ReplaceModelsForTypeAgentDto {
  /**
   * Danh sách model registry IDs mới (thay thế toàn bộ)
   */
  @ApiProperty({
    description: 'Danh sách model registry IDs mới để thay thế toàn bộ models của type agent',
    type: [String],
    example: ['550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'],
  })
  @IsArray()
  @IsUUID(4, { each: true, message: 'Mỗi modelRegistryId phải là UUID hợp lệ' })
  modelRegistryIds: string[];
}

/**
 * DTO cho việc xóa models khỏi type agent
 */
export class RemoveModelsFromTypeAgentDto {
  /**
   * <PERSON>h sách model registry IDs cần xóa
   */
  @ApiProperty({
    description: 'Danh sách model registry IDs cần xóa khỏi type agent',
    type: [String],
    example: ['550e8400-e29b-41d4-a716-************'],
  })
  @IsArray()
  @IsUUID(4, { each: true, message: 'Mỗi modelRegistryId phải là UUID hợp lệ' })
  modelRegistryIds: string[];
}

/**
 * DTO cho query parameters của models listing
 */
export class TypeAgentModelsQueryDto {
  /**
   * Số trang (bắt đầu từ 1)
   */
  @ApiPropertyOptional({
    description: 'Số trang (bắt đầu từ 1)',
    example: 1,
    minimum: 1,
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsInt()
  @Min(1)
  page?: number = 1;

  /**
   * Số lượng items per page
   */
  @ApiPropertyOptional({
    description: 'Số lượng items per page',
    example: 10,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsInt()
  @Min(1)
  limit?: number = 10;

  /**
   * Tìm kiếm theo tên model
   */
  @ApiPropertyOptional({
    description: 'Tìm kiếm theo tên model',
    example: 'gpt-4',
  })
  @IsOptional()
  @IsString()
  search?: string;
}

/**
 * DTO cho thông tin chi tiết model
 */
export class TypeAgentModelItemDto {
  /**
   * ID của model registry
   */
  @ApiProperty({
    description: 'ID của model registry',
    example: '550e8400-e29b-41d4-a716-************',
  })
  id: string;

  /**
   * Model ID
   */
  @ApiProperty({
    description: 'Model ID',
    example: 'gpt-4-turbo-preview',
  })
  modelNamePatten: string;

  /**
   * Provider của model
   */
  @ApiProperty({
    description: 'Provider của model',
    example: 'OpenAI',
  })
  provider: ProviderEnum;

  /**
   * Trạng thái của model
   */
  @ApiProperty({
    description: 'Trạng thái của model',
    example: 'ACTIVE',
  })
  status: string;

  /**
   * Ngày tạo
   */
  @ApiProperty({
    description: 'Ngày tạo model registry',
    example: 1682506892000,
  })
  createdAt: number;
}
