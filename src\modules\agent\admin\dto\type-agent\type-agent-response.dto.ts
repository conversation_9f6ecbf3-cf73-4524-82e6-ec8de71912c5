import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { TypeAgentStatus } from '@modules/agent/constants';
import { TypeAgentConfig } from '@modules/agent/interfaces/type-agent-config.interface';
import { EmployeeInfoDto } from '@modules/agent/admin/dto/common';
import { AgentSystemDto } from '@modules/agent/admin/dto/agent-system';
/**
 * DTO cho thông tin tool trong type agent detail
 */
export class TypeAgentToolDto {
  /**
   * ID của tool
   */
  @ApiProperty({
    description: 'ID của tool',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  /**
   * Tên của tool
   */
  @ApiProperty({
    description: 'Tên của tool',
    example: 'Search Tool',
  })
  name: string;

  /**
   * Mô tả của tool
   */
  @ApiPropertyOptional({
    description: 'Mô tả của tool',
    example: 'Công cụ tìm kiếm thông tin',
  })
  description: string | null;

  /**
   * Tên version mặc định
   */
  @ApiPropertyOptional({
    description: 'Tên version mặc định',
    example: 'v1.0.0',
  })
  versionName: string | null;

  /**
   * ID version mặc định
   */
  @ApiPropertyOptional({
    description: 'ID version mặc định',
    example: '550e8400-e29b-41d4-a716-446655440001',
  })
  versionId: string | null;
}

/**
 * DTO cho thông tin loại agent trong danh sách
 */
export class TypeAgentListItemDto {
  /**
   * ID của loại agent
   */
  @ApiProperty({
    description: 'ID của loại agent',
    example: 1,
  })
  id: number;

  /**
   * Tên loại agent
   */
  @ApiProperty({
    description: 'Tên loại agent',
    example: 'Chatbot Agent',
  })
  name: string;

  /**
   * Mô tả chi tiết về loại agent
   */
  @ApiPropertyOptional({
    description: 'Mô tả chi tiết về loại agent',
    example: 'Loại agent hỗ trợ chat với người dùng',
  })
  description: string | null;

  /**
   * Thời gian tạo
   */
  @ApiProperty({
    description: 'Thời gian tạo (timestamp millis)',
    example: 1682506892000,
  })
  createdAt: number;

  /**
   * Trạng thái của loại agent
   */
  @ApiProperty({
    description: 'Trạng thái của loại agent',
    enum: TypeAgentStatus,
    example: TypeAgentStatus.DRAFT,
  })
  status: TypeAgentStatus;

  /**
   * Danh sách tool IDs được gán cho type agent
   */
  @ApiProperty({
    description: 'Danh sách tool IDs được gán cho type agent',
    type: [String],
    example: ['550e8400-e29b-41d4-a716-446655440000', '550e8400-e29b-41d4-a716-446655440001'],
  })
  toolIds: string[];

  /**
   * Danh sách model registry IDs được gán cho type agent
   */
  @ApiProperty({
    description: 'Danh sách model registry IDs được gán cho type agent',
    type: [String],
    example: ['550e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440003'],
  })
  modelIds: string[];
}

/**
 * DTO cho thông tin chi tiết loại agent
 */ 
export class TypeAgentDetailDto extends TypeAgentListItemDto {
  /**
   * Cấu hình mặc định cho loại agent
   */
  @ApiProperty({
    description: 'Cấu hình mặc định cho loại agent',
    example: {
      hasProfile: true,
      hasOutput: true,
      hasConversion: false,
      hasResources: true,
    },
  })
  defaultConfig: TypeAgentConfig;

  /**
   * Danh sách agent system của loại agent
   */
  @ApiProperty({
    description: 'Danh sách agent system của loại agent',
    type: [AgentSystemDto],
  })
  agentSystems: AgentSystemDto[];

  /**
   * Thời gian cập nhật
   */
  @ApiProperty({
    description: 'Thời gian cập nhật (timestamp millis)',
    example: 1682506892000,
  })
  updatedAt: number;

  /**
   * Thông tin người tạo
   */
  @ApiPropertyOptional({
    description: 'Thông tin người tạo',
    type: EmployeeInfoDto,
  })
  created?: EmployeeInfoDto;

  /**
   * Thông tin người cập nhật
   */
  @ApiPropertyOptional({
    description: 'Thông tin người cập nhật',
    type: EmployeeInfoDto,
  })
  updated?: EmployeeInfoDto;
}
