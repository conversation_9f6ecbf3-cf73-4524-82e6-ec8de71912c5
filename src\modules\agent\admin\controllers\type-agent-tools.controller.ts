import { Controller, Get, Post, Delete, Body, Param, ParseIntPipe } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBody } from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards/jwt-employee.guard';
import { UseGuards } from '@nestjs/common';
import { ApiBearerAuth } from '@nestjs/swagger';
import { ApiResponseDto } from '@common/response';
import { TypeAgentToolsService } from '../services/type-agent-tools.service';
import { 
  AddToolsToTypeAgentDto, 
  RemoveToolsFromTypeAgentDto, 
  TypeAgentToolsResponseDto 
} from '../dto/type-agent/type-agent-tools.dto';

/**
 * Controller quản lý tools cho type agent
 */
@ApiTags('Admin - Type Agent Tools')
@Controller('admin/type-agent-tools')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
export class TypeAgentToolsController {
  constructor(private readonly typeAgentToolsService: TypeAgentToolsService) {}

  /**
   * Lấy danh sách tools của type agent
   */
  @Get(':typeAgentId')
  @ApiOperation({
    summary: 'Lấy danh sách tools của type agent',
    description: 'API để lấy danh sách tất cả tools được gán cho một type agent cụ thể'
  })
  @ApiParam({
    name: 'typeAgentId',
    description: 'ID của type agent',
    example: 1,
    type: 'integer'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách tools thành công',
    schema: ApiResponseDto.getSchema(TypeAgentToolsResponseDto)
  })
  async getToolsByTypeAgent(
    @Param('typeAgentId', ParseIntPipe) typeAgentId: number
  ): Promise<ApiResponseDto<TypeAgentToolsResponseDto>> {
    const result = await this.typeAgentToolsService.getToolsByTypeAgent(typeAgentId);
    return ApiResponseDto.success(result, 'Lấy danh sách tools thành công');
  }

  /**
   * Thêm tools vào type agent
   */
  @Post('add')
  @ApiOperation({
    summary: 'Thêm tools vào type agent',
    description: 'API để thêm một hoặc nhiều tools vào type agent'
  })
  @ApiBody({
    description: 'Dữ liệu thêm tools',
    type: AddToolsToTypeAgentDto,
    examples: {
      addSingleTool: {
        summary: 'Thêm một tool',
        value: {
          typeAgentId: 1,
          toolIds: ['550e8400-e29b-41d4-a716-446655440000']
        }
      },
      addMultipleTools: {
        summary: 'Thêm nhiều tools',
        value: {
          typeAgentId: 1,
          toolIds: [
            '550e8400-e29b-41d4-a716-446655440000',
            '550e8400-e29b-41d4-a716-446655440001'
          ]
        }
      }
    }
  })
  @ApiResponse({
    status: 201,
    description: 'Thêm tools thành công',
    schema: ApiResponseDto.getSchema(null)
  })
  async addToolsToTypeAgent(
    @Body() addDto: AddToolsToTypeAgentDto
  ): Promise<ApiResponseDto<null>> {
    await this.typeAgentToolsService.addToolsToTypeAgent(addDto.typeAgentId, addDto.toolIds);
    return ApiResponseDto.created(null, 'Thêm tools vào type agent thành công');
  }

  /**
   * Xóa tools khỏi type agent
   */
  @Delete('remove')
  @ApiOperation({
    summary: 'Xóa tools khỏi type agent',
    description: 'API để xóa một hoặc nhiều tools khỏi type agent'
  })
  @ApiBody({
    description: 'Dữ liệu xóa tools',
    type: RemoveToolsFromTypeAgentDto,
    examples: {
      removeSingleTool: {
        summary: 'Xóa một tool',
        value: {
          typeAgentId: 1,
          toolIds: ['550e8400-e29b-41d4-a716-446655440000']
        }
      },
      removeMultipleTools: {
        summary: 'Xóa nhiều tools',
        value: {
          typeAgentId: 1,
          toolIds: [
            '550e8400-e29b-41d4-a716-446655440000',
            '550e8400-e29b-41d4-a716-446655440001'
          ]
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: 'Xóa tools thành công',
    schema: ApiResponseDto.getSchema(null)
  })
  async removeToolsFromTypeAgent(
    @Body() removeDto: RemoveToolsFromTypeAgentDto
  ): Promise<ApiResponseDto<null>> {
    await this.typeAgentToolsService.removeToolsFromTypeAgent(removeDto.typeAgentId, removeDto.toolIds);
    return ApiResponseDto.success(null, 'Xóa tools khỏi type agent thành công');
  }

  /**
   * Xóa tất cả tools khỏi type agent
   */
  @Delete(':typeAgentId/all')
  @ApiOperation({
    summary: 'Xóa tất cả tools khỏi type agent',
    description: 'API để xóa tất cả tools khỏi một type agent cụ thể'
  })
  @ApiParam({
    name: 'typeAgentId',
    description: 'ID của type agent',
    example: 1,
    type: 'integer'
  })
  @ApiResponse({
    status: 200,
    description: 'Xóa tất cả tools thành công',
    schema: ApiResponseDto.getSchema(null)
  })
  async removeAllToolsFromTypeAgent(
    @Param('typeAgentId', ParseIntPipe) typeAgentId: number
  ): Promise<ApiResponseDto<null>> {
    await this.typeAgentToolsService.removeAllToolsFromTypeAgent(typeAgentId);
    return ApiResponseDto.success(null, 'Xóa tất cả tools khỏi type agent thành công');
  }
}
