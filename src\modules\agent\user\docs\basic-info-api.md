# Agent Basic Info APIs

## Tổng Quan

Basic Info APIs cho phép người dùng quản lý thông tin cơ bản của agent, bao gồm avatar, tên, model configuration, instruction, và vector store settings.

## Cấu Trúc Database

Basic info được lưu trữ trong nhiều bảng:
- **agents**: `avatar`, `name`, `model_config`, `instruction`, `vector_store_id`
- **agents_user**: `user_model_id`, `system_model_id`, `model_fine_tune_id`

## API Endpoints

### 1. Lấy Thông Tin Basic Info

**GET** `/user/agents/{id}/basic-info`

Lấy thông tin basic info hiện tại của agent.

**Parameters:**
- `id` (path): ID của agent (UUID)

**Response:**
```json
{
  "success": true,
  "data": {
    "avatar": "https://cdn.example.com/avatars/agent-avatar.jpg",
    "name": "AI Assistant Marketing",
    "provider": "OPENAI",
    "keyLlm": "key-llm-uuid-123",
    "modelId": "gpt-4o",
    "modelConfig": {
      "temperature": 0.7,
      "top_p": 0.9,
      "top_k": 40,
      "top_k": 40
    },
    "instruction": "Bạn là trợ lý AI chuyên về marketing. Hãy giúp người dùng tạo nội dung marketing hiệu quả.",
    "vectorId": "vector-store-uuid-123",
    "updatedAt": 1672531200000
  },
  "message": "Lấy thông tin basic info thành công"
}
```

### 2. Cập Nhật Basic Info

**PUT** `/user/agents/{id}/basic-info`

Cập nhật thông tin basic info của agent. Tất cả các trường đều optional.

**Parameters:**
- `id` (path): ID của agent (UUID)

**Request Body:**
```json
{
  "name": "AI Assistant Marketing Pro",
  "avatarFile": {
    "fileName": "avatar.jpg",
    "mimeType": "image/jpeg"
  },
  "userModelId": "user-model-uuid-123",
  "modelConfig": {
    "temperature": 0.8,
    "top_p": 0.95,
    "top_k": 50,
    "top_k": 50
  },
  "instruction": "Bạn là trợ lý AI chuyên về marketing. Hãy giúp người dùng tạo nội dung marketing hiệu quả và sáng tạo.",
  "vectorStoreId": "new-vector-store-uuid-456"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "avatar": "https://cdn.example.com/avatars/new-agent-avatar.jpg",
    "name": "AI Assistant Marketing Pro",
    "provider": "OPENAI",
    "keyLlm": "user-key-uuid-456",
    "modelId": "gpt-4o",
    "modelConfig": {
      "temperature": 0.8,
      "top_p": 0.95,
      "top_k": 50,
      "top_k": 50
    },
    "instruction": "Bạn là trợ lý AI chuyên về marketing. Hãy giúp người dùng tạo nội dung marketing hiệu quả và sáng tạo.",
    "vectorId": "new-vector-store-uuid-456",
    "updatedAt": 1672531300000,
    "avatarUpload": {
      "uploadUrl": "https://s3.amazonaws.com/bucket/upload-url",
      "publicUrl": "https://s3.amazonaws.com/bucket/avatar.jpg"
    }
  },
  "message": "Cập nhật basic info thành công"
}
```

## Validation Rules

### **Name**
- **Type**: String
- **Max Length**: 255 ký tự
- **Optional**: Có

### **Avatar File**
- **Type**: Object
- **Properties**:
  - `fileName`: String - Tên file avatar
  - `mimeType`: String - MIME type (image/jpeg, image/png, image/webp, image/gif)
- **Optional**: Có
- **Note**: Khi cung cấp, sẽ trả về `avatarUpload` object trong response

### **Model Selection (Chỉ chọn 1)**
- **userModelId**: UUID - Sử dụng user model
- **systemModelId**: UUID - Sử dụng system model  
- **modelFineTuneId**: UUID - Sử dụng fine-tuned model
- **Logic**: Khi set một model, các model khác sẽ được set null

### **Model Config**
- **temperature**: Number (0-2), default: 0.7
- **top_p**: Number (0-1), default: 0.9
- **top_k**: Number (≥0), default: 40

- **Merge Logic**: Merge với config hiện tại

### **Instruction**
- **Type**: String
- **Max Length**: 10,000 ký tự
- **Optional**: Có

### **Vector Store ID**
- **Type**: String
- **Max Length**: 100 ký tự
- **Optional**: Có

## Model Resolution Logic

### **Priority Order:**
1. **user_model_id** → Query `user_models` table
   - `model_id`: từ `user_models.model_id`
   - `provider`: `USER_KEY`
   - `keyLlm`: từ `user_models.key_llm_id` ✅ **CHỈ CÓ KHI CÓ userModelId**

2. **system_model_id** → Query `system_models` table
   - `model_id`: từ `system_models.model_id`
   - `provider`: `SYSTEM_KEY`
   - `keyLlm`: `null` ❌ **KHÔNG CÓ keyLlm**

3. **model_fine_tune_id** → Query `user_model_fine_tune` table
   - `model_id`: từ `user_model_fine_tune.model_id`
   - `provider`: `FINE_TUNED`
   - `keyLlm`: `null` ❌ **KHÔNG CÓ keyLlm**

4. **None** → No model configured
   - `model_id`: empty string
   - `provider`: `unknown`
   - `keyLlm`: `null`

### **Database Queries:**
```sql
-- Priority 1: User Model (có keyLlm)
SELECT um.model_id, um.key_llm_id
FROM user_models um
WHERE um.id = :userModelId

-- Priority 2: System Model (không có keyLlm)
SELECT sm.model_id
FROM system_models sm
WHERE sm.id = :systemModelId

-- Priority 3: Fine-tune Model (không có keyLlm)
SELECT umft.model_id
FROM user_model_fine_tune umft
WHERE umft.id = :modelFineTuneId
```

### **Key LLM Logic:**
- **keyLlmId chỉ có giá trị khi userModelId tồn tại**
- Nếu systemModelId hoặc modelFineTuneId → keyLlmId = null
- Lấy từ `user_models.key_llm_id` field
- Logic: `keyLlmId = userModelId ? user_models.key_llm_id : null`

## Error Codes

- `40101`: Agent không tồn tại hoặc không thuộc về user
- `40135`: Cấu hình model không hợp lệ
- `40136`: Không thể tạo S3 key cho avatar
- `40120`: Lỗi khi thao tác với tài nguyên agent

## Security

- Tất cả APIs yêu cầu JWT authentication
- User chỉ có thể thao tác với agents thuộc về mình
- Validation nghiêm ngặt cho tất cả input parameters
- S3 upload URLs có thời hạn (1 ngày)

## Business Logic

### **Avatar Upload Flow**
1. Client gửi `avatarFile` object với `fileName` và `mimeType` trong request
2. Server tạo S3 key từ fileName và upload URL
3. Server cập nhật `avatar` field với S3 key
4. Server trả về `avatarUpload` object với `uploadUrl` và `publicUrl` trong response
5. Client upload file lên `uploadUrl`
6. Client có thể sử dụng `publicUrl` để hiển thị avatar

### **Model Configuration Merge**
- Merge với config hiện tại thay vì replace hoàn toàn
- Chỉ update các fields được gửi trong request
- Validate ranges cho tất cả numeric fields

### **Model Selection Logic**
- Chỉ một model type được active tại một thời điểm
- Khi set model mới, các model khác được set null
- Provider được resolve tự động từ model type

### **Data Persistence**
- Agents table: `avatar`, `name`, `model_config`, `instruction`, `vector_store_id`
- Agents_user table: `user_model_id`, `system_model_id`, `model_fine_tune_id`
- Transaction support đảm bảo data consistency

## CDN Integration

### **Avatar URLs**
- Sử dụng CDN service để tạo view URLs
- URLs có thời hạn 1 ngày
- Fallback gracefully nếu CDN service lỗi

### **Upload URLs**
- S3 pre-signed URLs cho upload
- Validate MIME types
- Auto-generate unique S3 keys
