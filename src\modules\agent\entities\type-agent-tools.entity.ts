import { <PERSON><PERSON><PERSON>, PrimaryColumn, Column } from 'typeorm';

/**
 * Entity cho bảng type_agent_tools - <PERSON><PERSON><PERSON>n lý mối quan hệ many-to-many giữa type agent và tools
 */
@Entity('type_agent_tools')
export class TypeAgentTools {
  /**
   * ID của type agent
   */
  @PrimaryColumn({ name: 'type_agent_id', type: 'integer' })
  typeAgentId: number;

  /**
   * ID của tool
   */
  @PrimaryColumn({ name: 'tool_id', type: 'uuid' })
  toolId: string;
}
