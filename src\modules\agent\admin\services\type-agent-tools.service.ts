import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { TypeAgentToolsRepository } from '@modules/agent/repositories/type-agent-tools.repository';
import { TypeAgentRepository } from '@modules/agent/repositories/type-agent.repository';
import { AdminToolRepository } from '@/modules/tools/repositories';
import { TypeAgentToolsResponseDto } from '../dto/type-agent/type-agent-tools.dto';
import { Transactional } from 'typeorm-transactional';

/**
 * Service xử lý logic nghiệp vụ cho Type Agent Tools
 */
@Injectable()
export class TypeAgentToolsService {
  private readonly logger = new Logger(TypeAgentToolsService.name);

  constructor(
    private readonly typeAgentToolsRepository: TypeAgentToolsRepository,
    private readonly typeAgentRepository: TypeAgentRepository,
    private readonly adminToolRepository: AdminToolRepository,
  ) {}

  /**
   * Lấy danh sách tools của type agent
   * @param typeAgentId ID của type agent
   * @returns Danh sách tools
   */
  async getToolsByTypeAgent(typeAgentId: number): Promise<TypeAgentToolsResponseDto> {
    try {
      this.logger.log(`Getting tools for type agent: ${typeAgentId}`);

      // Validate type agent exists
      await this.validateTypeAgentExists(typeAgentId);

      // Lấy danh sách tool IDs
      const toolIds = await this.typeAgentToolsRepository.getToolIdsByTypeAgent(typeAgentId);

      return {
        typeAgentId,
        toolIds,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error getting tools for type agent ${typeAgentId}: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_FETCH_FAILED);
    }
  }

  /**
   * Thêm tools vào type agent
   * @param typeAgentId ID của type agent
   * @param toolIds Danh sách tool IDs
   */
  @Transactional()
  async addToolsToTypeAgent(typeAgentId: number, toolIds: string[]): Promise<void> {
    try {
      this.logger.log(`Adding tools to type agent ${typeAgentId}: ${toolIds.join(', ')}`);

      // Validate type agent exists
      await this.validateTypeAgentExists(typeAgentId);

      // Validate tools exist
      await this.validateToolsExist(toolIds);

      // Thêm từng tool vào type agent
      for (const toolId of toolIds) {
        await this.typeAgentToolsRepository.addToolToTypeAgent(typeAgentId, toolId);
      }

      this.logger.log(`Successfully added ${toolIds.length} tools to type agent ${typeAgentId}`);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error adding tools to type agent ${typeAgentId}: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Xóa tools khỏi type agent
   * @param typeAgentId ID của type agent
   * @param toolIds Danh sách tool IDs
   */
  @Transactional()
  async removeToolsFromTypeAgent(typeAgentId: number, toolIds: string[]): Promise<void> {
    try {
      this.logger.log(`Removing tools from type agent ${typeAgentId}: ${toolIds.join(', ')}`);

      // Validate type agent exists
      await this.validateTypeAgentExists(typeAgentId);

      // Xóa từng tool khỏi type agent
      for (const toolId of toolIds) {
        await this.typeAgentToolsRepository.removeToolFromTypeAgent(typeAgentId, toolId);
      }

      this.logger.log(`Successfully removed ${toolIds.length} tools from type agent ${typeAgentId}`);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error removing tools from type agent ${typeAgentId}: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Xóa tất cả tools khỏi type agent
   * @param typeAgentId ID của type agent
   */
  @Transactional()
  async removeAllToolsFromTypeAgent(typeAgentId: number): Promise<void> {
    try {
      this.logger.log(`Removing all tools from type agent ${typeAgentId}`);

      // Validate type agent exists
      await this.validateTypeAgentExists(typeAgentId);

      // Xóa tất cả tools
      await this.typeAgentToolsRepository.removeAllToolsFromTypeAgent(typeAgentId);

      this.logger.log(`Successfully removed all tools from type agent ${typeAgentId}`);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error removing all tools from type agent ${typeAgentId}: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Validate type agent exists
   * @param typeAgentId ID của type agent
   */
  private async validateTypeAgentExists(typeAgentId: number): Promise<void> {
    const typeAgent = await this.typeAgentRepository.findOne({
      where: { id: typeAgentId }
    });

    if (!typeAgent) {
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
    }
  }

  /**
   * Validate tools exist
   * @param toolIds Danh sách tool IDs
   */
  private async validateToolsExist(toolIds: string[]): Promise<void> {
    for (const toolId of toolIds) {
      const tool = await this.adminToolRepository.findOne({
        where: { id: toolId }
      });

      if (!tool) {
        throw new AppException(
          AGENT_ERROR_CODES.AGENT_CREATION_FAILED,
          `Tool với ID ${toolId} không tồn tại`
        );
      }
    }
  }
}
