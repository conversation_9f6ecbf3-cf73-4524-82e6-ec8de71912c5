import { PaginatedResult } from '@/common/response';
import { AppException } from '@common/exceptions';
import { Agent, AgentMemories, AgentTemplate } from '@modules/agent/entities';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions/agent-error.code';
import { AgentMemoriesRepository } from '@modules/agent/repositories/agent-memories.repository';
import { AgentStrategyRepository } from '@modules/agent/repositories/agent-strategy.repository';
import { AgentTemplateRepository } from '@modules/agent/repositories/agent-template.repository';
import { AgentRepository } from '@modules/agent/repositories/agent.repository';
import { TypeAgentRepository } from '@modules/agent/repositories/type-agent.repository';
import { EmployeeInfoService } from '@modules/employee/services/employee-info.service';
import { ProductRepository } from '@modules/marketplace/repositories/product.repository';
import { MODELS_ERROR_CODES } from '@modules/models/exceptions/models.exception';
import { SystemModelsRepository } from '@modules/models/repositories/system-models.repository';
import { VectorStoreRepository } from '@modules/data/knowledge-files/repositories/vector-store.repository';
import { Injectable, Logger } from '@nestjs/common';
import { CdnService } from '@shared/services/cdn.service';
import { S3Service } from '@shared/services/s3.service';
import { FileSizeEnum } from '@shared/utils/file/file-size.util';
import { ImageType } from '@shared/utils/file/image-media_type.util';
import { CategoryFolderEnum, generateS3Key } from '@shared/utils/generators/s3-key-generator.util';
import { TimeIntervalEnum } from '@utils/time';
import { Transactional } from 'typeorm-transactional';
import {
  AgentTemplateDetailDto,
  AgentTemplateListItemDto,
  AgentTemplateQueryDto,
  CreateAgentTemplateDto,
  DeletedAgentTemplateQueryDto,
  RestoreAgentTemplateDto,
  UpdateAgentTemplateDto,
} from '../dto/agent-template';

/**
 * Service xử lý các thao tác liên quan đến mẫu agent cho admin
 */
@Injectable()
export class AdminAgentTemplateService {
  private readonly logger = new Logger(AdminAgentTemplateService.name);

  constructor(
    private readonly agentTemplateRepository: AgentTemplateRepository,
    private readonly agentRepository: AgentRepository,
    private readonly agentMemoriesRepository: AgentMemoriesRepository,
    private readonly typeAgentRepository: TypeAgentRepository,
    private readonly agentStrategyRepository: AgentStrategyRepository,
    private readonly systemModelsRepository: SystemModelsRepository,
    private readonly vectorStoreRepository: VectorStoreRepository,
    private readonly employeeInfoService: EmployeeInfoService,
    private readonly productRepository: ProductRepository,
    private readonly cdnService: CdnService,
    private readonly s3Service: S3Service,
  ) { }

  /**
   * Tạo mẫu agent mới
   * @param createDto Dữ liệu tạo mẫu agent
   * @param employeeId ID của nhân viên tạo
   * @returns ID của mẫu agent đã tạo
   */
  @Transactional()
  async create(
    createDto: CreateAgentTemplateDto,
    employeeId: number,
  ): Promise<{ id: string; avatarUrlUpload: string | null }> {
    // Kiểm tra type agent có tồn tại không
    const typeAgent = await this.typeAgentRepository.findById(createDto.typeId);
    if (!typeAgent) {
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
    }

    // Xử lý backward compatibility: modelBaseId -> modelSystemId
    if (createDto.modelBaseId && !createDto.modelSystemId) {
      createDto.modelSystemId = createDto.modelBaseId;
      this.logger.warn(`modelBaseId is deprecated. Use modelSystemId instead. Converting modelBaseId: ${createDto.modelBaseId} to modelSystemId`);
    }

    // Kiểm tra system model có tồn tại không (nếu có)
    if (createDto.modelSystemId) {
      const systemModelExists = await this.systemModelsRepository.isExists(createDto.modelSystemId);
      if (!systemModelExists) {
        throw new AppException(MODELS_ERROR_CODES.MODEL_NOT_FOUND, 'System model không tồn tại');
      }
    }

    // Kiểm tra strategy có tồn tại và chưa bị xóa không (nếu có)
    if (createDto.strategyId) {
      const strategyCheck = await this.agentStrategyRepository.checkExistenceAndUsing(createDto.strategyId);
      if (!strategyCheck) {
        throw new AppException(AGENT_ERROR_CODES.STRATEGY_NOT_FOUND, 'Strategy không tồn tại hoặc đã bị xóa');
      }
    }

    // Kiểm tra vector store có tồn tại không (nếu có)
    if (createDto.vectorStoreId) {
      const vectorStoreExists = await this.vectorStoreRepository.findOne({
        where: { id: createDto.vectorStoreId }
      });
      if (!vectorStoreExists) {
        throw new AppException(AGENT_ERROR_CODES.VECTOR_STORE_NOT_FOUND, 'Vector store không tồn tại');
      }
    }

    try {
      // Tạo agent trước
      const agentData: Partial<Agent> = {
        name: createDto.name,
        modelConfig: createDto.modelConfig,
        instruction: createDto.instruction,
      };

      // Tạo URL upload cho avatar nếu có MIME type
      let avatarUrlUpload: string | null = null;
      if (createDto.avatarMimeType) {
        const avatarKey = generateS3Key({
          baseFolder: employeeId.toString(),
          categoryFolder: CategoryFolderEnum.AGENT,
          useTimeFolder: true,
        });

        avatarUrlUpload = await this.s3Service.createPresignedWithID(
          avatarKey,
          TimeIntervalEnum.ONE_HOUR,
          ImageType.getType(createDto.avatarMimeType),
          FileSizeEnum.FIVE_MB,
        );

        agentData.avatar = avatarKey;
      };

      const savedAgent = await this.agentRepository.save(agentData);

      // Tạo agent template
      const templateData: Partial<AgentTemplate> = {
        id: savedAgent.id,
        typeId: createDto.typeId,
        profile: createDto.profile || {},
        convertConfig: createDto.conversion || [],
        modelSystemId: createDto.modelSystemId,
        strategyId: createDto.strategyId,
        createdBy: employeeId,
        updatedBy: employeeId,
      };

      await this.agentTemplateRepository.save(templateData);

      // Lưu memories nếu có
      if (createDto.memories && createDto.memories.length > 0) {
        const memoriesData: Partial<AgentMemories>[] = createDto.memories.map(memory => ({
          agentId: savedAgent.id,
          structuredContent: {
            title: memory.title,
            reason: memory.reason,
            content: memory.content,
          },
          createdAt: Date.now(),
        }));

        await this.agentMemoriesRepository.save(memoriesData);
        this.logger.log(`Saved ${memoriesData.length} memories for agent template: ${savedAgent.id}`);
      }

      this.logger.log(`Created agent template with ID: ${savedAgent.id}`);
      return {
        id: savedAgent.id,
        avatarUrlUpload: avatarUrlUpload
      };

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error creating agent template: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_TEMPLATE_CREATE_FAILED);
    }
  }

  /**
   * Lấy danh sách mẫu agent với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách mẫu agent với phân trang
   */
  async findAll(queryDto: AgentTemplateQueryDto): Promise<PaginatedResult<AgentTemplateListItemDto>> {
    try {
      const result = await this.agentTemplateRepository.findPaginated(
        queryDto.page,
        queryDto.limit,
        queryDto.search,
        queryDto.sortBy,
        queryDto.sortDirection,
      );

      // Map to DTO với dữ liệu đã được JOIN
      const items = await Promise.all(
        result.items.map(template => this.mapToListItemDtoOptimized(template))
      );

      return {
        items,
        meta: result.meta,
      };

    } catch (error) {
      this.logger.error(`Error getting agent templates: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_TEMPLATE_FETCH_FAILED);
    }
  }

  /**
   * Lấy chi tiết mẫu agent theo ID
   * @param id ID của mẫu agent
   * @returns Chi tiết mẫu agent
   */
  async findById(id: string): Promise<AgentTemplateDetailDto> {
    const template = await this.agentTemplateRepository.findByIdWithDetails(id);
    if (!template) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_TEMPLATE_NOT_FOUND);
    }

    // Kiểm tra template đã bị xóa mềm hay chưa
    if (template.deletedBy) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_TEMPLATE_ALREADY_DELETED);
    }

    try {
      return await this.mapToDetailDtoOptimized(template);
    } catch (error) {
      this.logger.error(`Error getting agent template detail: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_TEMPLATE_FETCH_FAILED);
    }
  }

  /**
   * Cập nhật mẫu agent
   * @param id ID của mẫu agent
   * @param updateDto Dữ liệu cập nhật
   * @param employeeId ID của nhân viên cập nhật
   * @returns ID của mẫu agent đã cập nhật
   */
  @Transactional()
  async update(
    id: string,
    updateDto: UpdateAgentTemplateDto,
    employeeId: number,
  ): Promise<{ id: string, avatarUrlUpload?: string | null }> {
    // Kiểm tra template có tồn tại không
    const template = await this.agentTemplateRepository.findById(id);
    if (!template) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_TEMPLATE_NOT_FOUND);
    }

    // Kiểm tra template đã bị xóa mềm hay chưa
    if (template.deletedBy) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_TEMPLATE_ALREADY_DELETED);
    }

    // Kiểm tra type agent nếu có cập nhật
    if (updateDto.typeId && updateDto.typeId !== template.typeId) {
      const typeAgent = await this.typeAgentRepository.findById(updateDto.typeId);
      if (!typeAgent) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }
    }

    // Xử lý backward compatibility: modelBaseId -> modelSystemId
    if (updateDto.modelBaseId !== undefined && updateDto.modelSystemId === undefined) {
      updateDto.modelSystemId = updateDto.modelBaseId;
      this.logger.warn(`modelBaseId is deprecated. Use modelSystemId instead. Converting modelBaseId: ${updateDto.modelBaseId} to modelSystemId`);
    }

    // Kiểm tra system model có tồn tại không (nếu có cập nhật và không phải null)
    if (updateDto.modelSystemId !== undefined && updateDto.modelSystemId !== template.modelSystemId) {
      if (updateDto.modelSystemId !== null) {
        const systemModelExists = await this.systemModelsRepository.isExists(updateDto.modelSystemId);
        if (!systemModelExists) {
          throw new AppException(MODELS_ERROR_CODES.MODEL_NOT_FOUND, 'System model không tồn tại');
        }
      }
    }

    // Kiểm tra strategy có tồn tại và chưa bị xóa không (nếu có cập nhật và không phải null)
    if (updateDto.strategyId !== undefined && updateDto.strategyId !== template.strategyId) {
      if (updateDto.strategyId !== null) {
        const strategyCheck = await this.agentStrategyRepository.checkExistenceAndUsing(updateDto.strategyId);
        if (!strategyCheck) {
          throw new AppException(AGENT_ERROR_CODES.STRATEGY_NOT_FOUND, 'Strategy không tồn tại hoặc đã bị xóa');
        }
      }
    }

    // Kiểm tra vector store có tồn tại không (nếu có cập nhật và không phải null)
    if (updateDto.vectorStoreId !== undefined) {
      if (updateDto.vectorStoreId !== null) {
        const vectorStoreExists = await this.vectorStoreRepository.findOne({
          where: { id: updateDto.vectorStoreId }
        });
        if (!vectorStoreExists) {
          throw new AppException(AGENT_ERROR_CODES.VECTOR_STORE_NOT_FOUND, 'Vector store không tồn tại');
        }
      }
    }

    try {
      // Cập nhật agent trước
      const agentUpdateData: Partial<Agent> = {};
      if (updateDto.name) agentUpdateData.name = updateDto.name;
      if (updateDto.modelConfig) agentUpdateData.modelConfig = updateDto.modelConfig;
      if (updateDto.instruction !== undefined) agentUpdateData.instruction = updateDto.instruction;
      if (updateDto.vectorStoreId !== undefined) agentUpdateData.vectorStoreId = updateDto.vectorStoreId;


      if (Object.keys(agentUpdateData).length > 0) {
        await this.agentRepository.updateAgent(id, agentUpdateData);
      }

      // Cập nhật agent template
      const templateUpdateData: Partial<AgentTemplate> = {};
      if (updateDto.typeId) templateUpdateData.typeId = updateDto.typeId;
      if (updateDto.profile) templateUpdateData.profile = updateDto.profile;

      if (updateDto.conversion) templateUpdateData.convertConfig = updateDto.conversion;
      if (updateDto.modelSystemId !== undefined) templateUpdateData.modelSystemId = updateDto.modelSystemId;
      if (updateDto.strategyId !== undefined) templateUpdateData.strategyId = updateDto.strategyId;
      if (updateDto.isForSale !== undefined) templateUpdateData.isForSale = updateDto.isForSale;
      templateUpdateData.updatedBy = employeeId;

      if (Object.keys(templateUpdateData).length > 1) { // > 1 vì luôn có updatedBy
        await this.agentTemplateRepository.update(id, templateUpdateData);
      }

      // Cập nhật memories nếu có
      if (updateDto.memories !== undefined) {
        // Xóa tất cả memories cũ
        await this.agentMemoriesRepository.deleteByAgentId(id);

        // Thêm memories mới nếu có
        if (updateDto.memories.length > 0) {
          const memoriesData: Partial<AgentMemories>[] = updateDto.memories.map(memory => ({
            agentId: id,
            structuredContent: {
              title: memory.title,
              reason: memory.reason,
              content: memory.content,
            },
            createdAt: Date.now(),
          }));

          await this.agentMemoriesRepository.save(memoriesData);
          this.logger.log(`Updated ${memoriesData.length} memories for agent template: ${id}`);
        } else {
          this.logger.log(`Cleared all memories for agent template: ${id}`);
        }
      }

      // Tạo URL upload cho avatar nếu có MIME type
      let avatarUrlUpload: string | null = null;
      if (updateDto.avatarMimeType) {
        const avatarKey = generateS3Key({
          baseFolder: employeeId.toString(),
          categoryFolder: CategoryFolderEnum.AGENT,
          useTimeFolder: true,
        });

        avatarUrlUpload = await this.s3Service.createPresignedWithID(
          avatarKey,
          TimeIntervalEnum.ONE_HOUR,
          ImageType.getType(updateDto.avatarMimeType),
          FileSizeEnum.FIVE_MB,
        );

        // Cập nhật avatar key cho agent
        await this.agentRepository.update(id, { avatar: avatarKey });
      }

      this.logger.log(`Updated agent template with ID: ${id}`);
      return { id, avatarUrlUpload };

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error updating agent template: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_TEMPLATE_UPDATE_FAILED);
    }
  }

  /**
   * Xóa mềm mẫu agent
   * @param id ID của mẫu agent
   * @param employeeId ID của nhân viên xóa
   * @returns ID của mẫu agent đã xóa
   */
  @Transactional()
  async softDelete(id: string, employeeId: number): Promise<{ id: string }> {
    // Kiểm tra template có tồn tại không
    const template = await this.agentTemplateRepository.findById(id);
    if (!template) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_TEMPLATE_NOT_FOUND);
    }

    // Kiểm tra template đã bị xóa mềm hay chưa
    if (template.deletedBy) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_TEMPLATE_ALREADY_DELETED);
    }

    try {
      // Nếu agent template có trạng thái isForSale = true, gỡ khỏi marketplace
      if (template.isForSale) {
        this.logger.log(`Agent template ${id} có trạng thái isForSale = true, đang gỡ khỏi marketplace...`);

        const productDeleted = await this.productRepository.deleteProductBySourceId(id);
        if (productDeleted) {
          this.logger.log(`Đã gỡ sản phẩm khỏi marketplace cho agent template: ${id}`);
        } else {
          this.logger.warn(`Không tìm thấy sản phẩm trong marketplace với sourceId: ${id}`);
        }
      }

      // Xóa mềm agent template
      await this.agentTemplateRepository.update(id, {
        deletedBy: employeeId,
        updatedBy: employeeId,
      });

      // Xóa mềm agent
      await this.agentRepository.update(id, {
        deletedAt: Date.now(),
      });

      // Xóa tất cả memories của agent
      const deletedMemoriesCount = await this.agentMemoriesRepository.deleteByAgentId(id);
      if (deletedMemoriesCount > 0) {
        this.logger.log(`Deleted ${deletedMemoriesCount} memories for agent template: ${id}`);
      }

      this.logger.log(`Soft deleted agent template with ID: ${id}`);
      return { id };

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error soft deleting agent template: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_TEMPLATE_DELETE_FAILED);
    }
  }

  /**
   * Lấy danh sách mẫu agent đã xóa với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách mẫu agent đã xóa với phân trang
   */
  async findDeleted(queryDto: DeletedAgentTemplateQueryDto): Promise<PaginatedResult<AgentTemplateListItemDto>> {
    try {
      const result = await this.agentTemplateRepository.findDeletedPaginated(
        queryDto.page,
        queryDto.limit,
        queryDto.search,
        queryDto.sortBy,
        queryDto.sortDirection,
      );

      // Map to DTO với dữ liệu đã được JOIN
      const items = await Promise.all(
        result.items.map(template => this.mapToListItemDtoOptimized(template))
      );

      return {
        items,
        meta: result.meta,
      };

    } catch (error) {
      this.logger.error(`Error getting deleted agent templates: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_TEMPLATE_FETCH_FAILED);
    }
  }

  /**
   * Lấy chi tiết mẫu agent đã xóa theo ID
   * @param id ID của mẫu agent
   * @returns Chi tiết mẫu agent đã xóa
   */
  async findDeletedById(id: string): Promise<AgentTemplateDetailDto> {
    const template = await this.agentTemplateRepository.findDeletedByIdWithDetails(id);
    if (!template) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_TEMPLATE_NOT_FOUND);
    }

    try {
      return await this.mapToDetailDtoOptimized(template);
    } catch (error) {
      this.logger.error(`Error getting deleted agent template detail: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_TEMPLATE_FETCH_FAILED);
    }
  }

  /**
   * Khôi phục mẫu agent đã xóa
   * @param restoreDto Dữ liệu khôi phục
   * @param employeeId ID của nhân viên khôi phục
   * @returns Số lượng mẫu agent đã khôi phục
   */
  @Transactional()
  async restore(
    restoreDto: RestoreAgentTemplateDto,
    employeeId: number,
  ): Promise<{ restoredCount: number }> {
    try {
      let restoredCount = 0;

      for (const id of restoreDto.ids) {
        // Kiểm tra template có tồn tại và đã bị xóa không
        const template = await this.agentTemplateRepository.findDeletedById(id);
        if (!template) {
          this.logger.warn(`Agent template with ID ${id} not found or not deleted`);
          continue;
        }

        // Khôi phục agent template
        const templateRestored = await this.agentTemplateRepository.restoreTemplate(id, employeeId);
        if (templateRestored > 0) {
          // Khôi phục agent
          await this.agentRepository.update(id, {
            deletedAt: null,
          });
          restoredCount++;
        }
      }

      this.logger.log(`Restored ${restoredCount} agent templates`);
      return { restoredCount };

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error restoring agent templates: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_TEMPLATE_RESTORE_FAILED);
    }
  }

  /**
   * Map AgentTemplate entity với dữ liệu JOIN to list item DTO (tối ưu)
   * @param template AgentTemplate entity với dữ liệu JOIN
   * @returns AgentTemplateListItemDto
   */
  private async mapToListItemDtoOptimized(template: AgentTemplate & { agent: any; typeAgent: any }): Promise<AgentTemplateListItemDto> {
    const dto = new AgentTemplateListItemDto();
    dto.id = template.id;
    dto.name = template.agent.name;
    dto.avatar = template.agent.avatar ? this.cdnService.generateUrlView(template.agent.avatar, TimeIntervalEnum.ONE_HOUR) : null;
    dto.typeId = template.typeId;
    dto.typeName = template.typeAgent?.name || 'Unknown Type';
    dto.modelId = (template as any).modelId || null;
    dto.isForSale = template.isForSale;
    dto.createdAt = template.agent.createdAt;
    dto.updatedAt = template.agent.updatedAt;

    return dto;
  }

  /**
   * Map AgentTemplate entity với dữ liệu JOIN to detail DTO (tối ưu)
   * @param template AgentTemplate entity với dữ liệu JOIN
   * @returns AgentTemplateDetailDto
   */
  private async mapToDetailDtoOptimized(template: AgentTemplate & { agent: any; typeAgent: any }): Promise<AgentTemplateDetailDto> {
    // Lấy thông tin nhân viên tạo
    const createdByInfo = template.createdBy
      ? await this.employeeInfoService.getEmployeeInfo(template.createdBy)
      : null;

    // Lấy thông tin nhân viên cập nhật
    const updatedByInfo = template.updatedBy
      ? await this.employeeInfoService.getEmployeeInfo(template.updatedBy)
      : null;

    // Lấy memories của agent
    const memories = await this.agentMemoriesRepository.findByAgentId(template.id);

    const dto = new AgentTemplateDetailDto();
    dto.id = template.id;
    dto.name = template.agent.name;
    dto.avatar = template.agent.avatar ? this.cdnService.generateUrlView(template.agent.avatar, TimeIntervalEnum.ONE_HOUR) : null;
    dto.modelConfig = template.agent.modelConfig;
    dto.instruction = template.agent.instruction;
    dto.typeId = template.typeId;
    dto.typeName = template.typeAgent?.name || 'Unknown Type';
    dto.profile = template.profile;
    dto.conversion = template.convertConfig;
    dto.modelSystemId = template.modelSystemId;
    dto.modelId = (template as any).modelId || null;
    dto.strategyId = template.strategyId;
    dto.isForSale = template.isForSale;
    dto.memories = memories.map(memory => ({
      title: memory.structuredContent.title || '',
      reason: memory.structuredContent.reason || '',
      content: memory.structuredContent.content,
    }));
    dto.createdBy = createdByInfo ? {
      employeeId: createdByInfo.id,
      name: createdByInfo.name,
      avatar: createdByInfo.avatar || '',
    } : null;
    dto.updatedBy = updatedByInfo ? {
      employeeId: updatedByInfo.id,
      name: updatedByInfo.name,
      avatar: updatedByInfo.avatar || '',
    } : null;
    dto.createdAt = template.agent.createdAt;
    dto.updatedAt = template.agent.updatedAt;

    return dto;
  }
}