import { ModelConfig } from '@modules/agent/interfaces/model-config.interface';
import { ModelConfigDto } from '../dto';

/**
 * Mapper cho các đối tượng ModelConfig
 */
export class ModelConfigMapper {
  /**
   * <PERSON>y<PERSON><PERSON> đổi từ ModelConfig sang ModelConfigDto (đã loại bỏ deprecated fields)
   * @param modelConfig Cấu hình model
   * @returns ModelConfigDto
   */
  static toDto(modelConfig?: ModelConfig): ModelConfigDto {
    return {
      temperature: modelConfig?.temperature || 0.7,
      top_p: modelConfig?.top_p || 0.9,
      top_k: modelConfig?.top_k || 40,
    };
  }
}
