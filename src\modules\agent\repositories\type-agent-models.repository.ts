import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { TypeAgentModels } from '../entities/type-agent-models.entity';
import { PaginatedResult } from '@common/response';

/**
 * Repository cho TypeAgentModels entity
 */
@Injectable()
export class TypeAgentModelsRepository extends Repository<TypeAgentModels> {
  constructor(private dataSource: DataSource) {
    super(TypeAgentModels, dataSource.createEntityManager());
  }

  /**
   * Lấy danh sách model registry IDs theo type agent ID
   * @param typeAgentId ID của type agent
   * @returns Danh sách model registry IDs
   */
  async getModelRegistryIdsByTypeAgent(typeAgentId: number): Promise<string[]> {
    const result = await this.createQueryBuilder('tam')
      .select('tam.model_registry_id', 'modelRegistryId')
      .where('tam.type_agent_id = :typeAgentId', { typeAgentId })
      .getRawMany();

    return result.map(row => row.modelRegistryId);
  }

  /**
   * <PERSON><PERSON><PERSON> danh sách type agent IDs theo model registry ID
   * @param modelRegistryId ID của model registry
   * @returns Danh sách type agent IDs
   */
  async getTypeAgentIdsByModelRegistry(modelRegistryId: string): Promise<number[]> {
    const result = await this.createQueryBuilder('tam')
      .select('tam.type_agent_id', 'typeAgentId')
      .where('tam.model_registry_id = :modelRegistryId', { modelRegistryId })
      .getRawMany();

    return result.map(row => row.typeAgentId);
  }

  /**
   * Thêm model registry vào type agent
   * @param typeAgentId ID của type agent
   * @param modelRegistryId ID của model registry
   */
  async addModelToTypeAgent(typeAgentId: number, modelRegistryId: string): Promise<void> {
    await this.createQueryBuilder()
      .insert()
      .into(TypeAgentModels)
      .values({ typeAgentId, modelRegistryId })
      .orIgnore()
      .execute();
  }

  /**
   * Xóa model registry khỏi type agent
   * @param typeAgentId ID của type agent
   * @param modelRegistryId ID của model registry
   */
  async removeModelFromTypeAgent(typeAgentId: number, modelRegistryId: string): Promise<void> {
    await this.createQueryBuilder()
      .delete()
      .from(TypeAgentModels)
      .where('type_agent_id = :typeAgentId AND model_registry_id = :modelRegistryId', { 
        typeAgentId, 
        modelRegistryId 
      })
      .execute();
  }

  /**
   * Xóa tất cả models của type agent
   * @param typeAgentId ID của type agent
   */
  async removeAllModelsFromTypeAgent(typeAgentId: number): Promise<void> {
    await this.createQueryBuilder()
      .delete()
      .from(TypeAgentModels)
      .where('type_agent_id = :typeAgentId', { typeAgentId })
      .execute();
  }

  /**
   * Kiểm tra xem type agent có model registry hay không
   * @param typeAgentId ID của type agent
   * @param modelRegistryId ID của model registry
   * @returns true nếu có, false nếu không
   */
  async hasModelInTypeAgent(typeAgentId: number, modelRegistryId: string): Promise<boolean> {
    const count = await this.createQueryBuilder('tam')
      .where('tam.type_agent_id = :typeAgentId AND tam.model_registry_id = :modelRegistryId', { 
        typeAgentId, 
        modelRegistryId 
      })
      .getCount();

    return count > 0;
  }

  /**
   * Thay thế tất cả models của type agent
   * @param typeAgentId ID của type agent
   * @param modelRegistryIds Danh sách model registry IDs mới
   */
  async replaceModelsForTypeAgent(typeAgentId: number, modelRegistryIds: string[]): Promise<void> {
    await this.manager.transaction(async (transactionalEntityManager) => {
      // Xóa tất cả models hiện tại
      await transactionalEntityManager
        .createQueryBuilder()
        .delete()
        .from(TypeAgentModels)
        .where('type_agent_id = :typeAgentId', { typeAgentId })
        .execute();

      // Thêm models mới
      if (modelRegistryIds.length > 0) {
        const values = modelRegistryIds.map(modelRegistryId => ({
          typeAgentId,
          modelRegistryId
        }));

        await transactionalEntityManager
          .createQueryBuilder()
          .insert()
          .into(TypeAgentModels)
          .values(values)
          .execute();
      }
    });
  }

  /**
   * Lấy danh sách models với thông tin chi tiết và phân trang
   * @param typeAgentId ID của type agent
   * @param page Số trang
   * @param limit Số lượng items per page
   * @param search Từ khóa tìm kiếm
   * @returns Danh sách models với phân trang
   */
  async getModelsWithDetailsByTypeAgent(
    typeAgentId: number,
    page: number = 1,
    limit: number = 10,
    search?: string
  ): Promise<PaginatedResult<any>> {
    const queryBuilder = this.createQueryBuilder('tam')
      .leftJoin('model_registry', 'model', 'model.id = tam.model_registry_id')
      .select([
        '"model"."id" AS id',
        '"model"."model_name_pattern" AS name',
        '"model"."model_name_pattern" AS "modelId"',
        '"model"."provider" AS provider',
        'CASE WHEN "model"."deleted_at" IS NULL THEN \'ACTIVE\' ELSE \'INACTIVE\' END AS status',
        'EXTRACT(EPOCH FROM "model"."created_at") * 1000 AS "createdAt"'
      ])
      .where('tam.type_agent_id = :typeAgentId', { typeAgentId })
      .andWhere('"model"."deleted_at" IS NULL'); // Chỉ lấy models chưa bị xóa

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      queryBuilder.andWhere(
        '("model"."model_name_pattern" ILIKE :search OR "model"."provider"::text ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Đếm tổng số records
    const totalItems = await queryBuilder.getCount();

    // Thêm phân trang
    const offset = (page - 1) * limit;
    queryBuilder
      .orderBy('"model"."created_at"', 'DESC')
      .limit(limit)
      .offset(offset);

    const items = await queryBuilder.getRawMany();

    return {
      items,
      meta: {
        currentPage: page,
        itemsPerPage: limit,
        totalItems,
        totalPages: Math.ceil(totalItems / limit),
        itemCount: items.length,
      },
    };
  }
}
