import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { TypeAgentModels } from '../entities/type-agent-models.entity';

/**
 * Repository cho TypeAgentModels entity
 */
@Injectable()
export class TypeAgentModelsRepository extends Repository<TypeAgentModels> {
  constructor(private dataSource: DataSource) {
    super(TypeAgentModels, dataSource.createEntityManager());
  }

  /**
   * Lấy danh sách model registry IDs theo type agent ID
   * @param typeAgentId ID của type agent
   * @returns Danh sách model registry IDs
   */
  async getModelRegistryIdsByTypeAgent(typeAgentId: number): Promise<string[]> {
    const result = await this.createQueryBuilder('tam')
      .select('tam.model_registry_id', 'modelRegistryId')
      .where('tam.type_agent_id = :typeAgentId', { typeAgentId })
      .getRawMany();

    return result.map(row => row.modelRegistryId);
  }

  /**
   * <PERSON><PERSON><PERSON> danh sách type agent IDs theo model registry ID
   * @param modelRegistryId ID của model registry
   * @returns Danh sách type agent IDs
   */
  async getTypeAgentIdsByModelRegistry(modelRegistryId: string): Promise<number[]> {
    const result = await this.createQueryBuilder('tam')
      .select('tam.type_agent_id', 'typeAgentId')
      .where('tam.model_registry_id = :modelRegistryId', { modelRegistryId })
      .getRawMany();

    return result.map(row => row.typeAgentId);
  }

  /**
   * Thêm model registry vào type agent
   * @param typeAgentId ID của type agent
   * @param modelRegistryId ID của model registry
   */
  async addModelToTypeAgent(typeAgentId: number, modelRegistryId: string): Promise<void> {
    await this.createQueryBuilder()
      .insert()
      .into(TypeAgentModels)
      .values({ typeAgentId, modelRegistryId })
      .orIgnore()
      .execute();
  }

  /**
   * Xóa model registry khỏi type agent
   * @param typeAgentId ID của type agent
   * @param modelRegistryId ID của model registry
   */
  async removeModelFromTypeAgent(typeAgentId: number, modelRegistryId: string): Promise<void> {
    await this.createQueryBuilder()
      .delete()
      .from(TypeAgentModels)
      .where('type_agent_id = :typeAgentId AND model_registry_id = :modelRegistryId', { 
        typeAgentId, 
        modelRegistryId 
      })
      .execute();
  }

  /**
   * Xóa tất cả models của type agent
   * @param typeAgentId ID của type agent
   */
  async removeAllModelsFromTypeAgent(typeAgentId: number): Promise<void> {
    await this.createQueryBuilder()
      .delete()
      .from(TypeAgentModels)
      .where('type_agent_id = :typeAgentId', { typeAgentId })
      .execute();
  }

  /**
   * Kiểm tra xem type agent có model registry hay không
   * @param typeAgentId ID của type agent
   * @param modelRegistryId ID của model registry
   * @returns true nếu có, false nếu không
   */
  async hasModelInTypeAgent(typeAgentId: number, modelRegistryId: string): Promise<boolean> {
    const count = await this.createQueryBuilder('tam')
      .where('tam.type_agent_id = :typeAgentId AND tam.model_registry_id = :modelRegistryId', { 
        typeAgentId, 
        modelRegistryId 
      })
      .getCount();

    return count > 0;
  }

  /**
   * Thay thế tất cả models của type agent
   * @param typeAgentId ID của type agent
   * @param modelRegistryIds Danh sách model registry IDs mới
   */
  async replaceModelsForTypeAgent(typeAgentId: number, modelRegistryIds: string[]): Promise<void> {
    await this.manager.transaction(async (transactionalEntityManager) => {
      // Xóa tất cả models hiện tại
      await transactionalEntityManager
        .createQueryBuilder()
        .delete()
        .from(TypeAgentModels)
        .where('type_agent_id = :typeAgentId', { typeAgentId })
        .execute();

      // Thêm models mới
      if (modelRegistryIds.length > 0) {
        const values = modelRegistryIds.map(modelRegistryId => ({
          typeAgentId,
          modelRegistryId
        }));

        await transactionalEntityManager
          .createQueryBuilder()
          .insert()
          .into(TypeAgentModels)
          .values(values)
          .execute();
      }
    });
  }
}
