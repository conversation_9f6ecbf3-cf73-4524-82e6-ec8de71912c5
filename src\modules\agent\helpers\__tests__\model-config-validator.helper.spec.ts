import { Test, TestingModule } from '@nestjs/testing';
import { ModelConfigValidatorHelper } from '../model-config-validator.helper';
import { SamplingParameterEnum } from '@modules/models/constants/model-capabilities.enum';
import { ModelConfig } from '@modules/agent/interfaces/model-config.interface';
import { AppException } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions/agent-error.code';

describe('ModelConfigValidatorHelper', () => {
  let mockSystemModelsRepository: any;

  beforeEach(async () => {
    mockSystemModelsRepository = {
      findById: jest.fn(),
    };
  });

  describe('validateAndFilterModelConfig', () => {
    it('should allow temperature when model supports it', () => {
      const modelConfig: ModelConfig = {
        temperature: 0.7,
        top_p: 0.9,
        max_tokens: 1000,
      };

      const allowedParameters = [SamplingParameterEnum.TEMPERATURE];

      const result = ModelConfigValidatorHelper.validateAndFilterModelConfig(
        modelConfig,
        allowedParameters
      );

      expect(result).toEqual({
        temperature: 0.7,
        max_tokens: 1000,
      });
    });

    it('should throw error when temperature is not supported', () => {
      const modelConfig: ModelConfig = {
        temperature: 0.7,
      };

      const allowedParameters = [SamplingParameterEnum.TOP_P];

      expect(() => {
        ModelConfigValidatorHelper.validateAndFilterModelConfig(
          modelConfig,
          allowedParameters
        );
      }).toThrow(AppException);
    });

    it('should validate temperature range (0-2)', () => {
      const modelConfig: ModelConfig = {
        temperature: 3.0, // Invalid range
      };

      const allowedParameters = [SamplingParameterEnum.TEMPERATURE];

      expect(() => {
        ModelConfigValidatorHelper.validateAndFilterModelConfig(
          modelConfig,
          allowedParameters
        );
      }).toThrow(AppException);
    });

    it('should allow top_p when model supports it', () => {
      const modelConfig: ModelConfig = {
        top_p: 0.9,
      };

      const allowedParameters = [SamplingParameterEnum.TOP_P];

      const result = ModelConfigValidatorHelper.validateAndFilterModelConfig(
        modelConfig,
        allowedParameters
      );

      expect(result).toEqual({
        top_p: 0.9,
      });
    });

    it('should validate top_p range (0-1)', () => {
      const modelConfig: ModelConfig = {
        top_p: 1.5, // Invalid range
      };

      const allowedParameters = [SamplingParameterEnum.TOP_P];

      expect(() => {
        ModelConfigValidatorHelper.validateAndFilterModelConfig(
          modelConfig,
          allowedParameters
        );
      }).toThrow(AppException);
    });

    it('should allow top_k when model supports it', () => {
      const modelConfig: ModelConfig = {
        top_k: 50,
      };

      const allowedParameters = [SamplingParameterEnum.TOP_K];

      const result = ModelConfigValidatorHelper.validateAndFilterModelConfig(
        modelConfig,
        allowedParameters
      );

      expect(result).toEqual({
        top_k: 50,
      });
    });

    it('should validate top_k range (>= 0)', () => {
      const modelConfig: ModelConfig = {
        top_k: -1, // Invalid range
      };

      const allowedParameters = [SamplingParameterEnum.TOP_K];

      expect(() => {
        ModelConfigValidatorHelper.validateAndFilterModelConfig(
          modelConfig,
          allowedParameters
        );
      }).toThrow(AppException);
    });

    it('should always allow max_tokens regardless of sampling parameters', () => {
      const modelConfig: ModelConfig = {
        max_tokens: 1000,
      };

      const allowedParameters = []; // No sampling parameters allowed

      const result = ModelConfigValidatorHelper.validateAndFilterModelConfig(
        modelConfig,
        allowedParameters
      );

      expect(result).toEqual({
        max_tokens: 1000,
      });
    });

    it('should validate max_tokens range (>= 1)', () => {
      const modelConfig: ModelConfig = {
        max_tokens: 0, // Invalid range
      };

      const allowedParameters = [];

      expect(() => {
        ModelConfigValidatorHelper.validateAndFilterModelConfig(
          modelConfig,
          allowedParameters
        );
      }).toThrow(AppException);
    });

    it('should filter out unsupported parameters and keep supported ones', () => {
      const modelConfig: ModelConfig = {
        temperature: 0.7,
        top_p: 0.9,
        top_k: 50,
        max_tokens: 1000,
      };

      const allowedParameters = [SamplingParameterEnum.TEMPERATURE, SamplingParameterEnum.TOP_K];

      const result = ModelConfigValidatorHelper.validateAndFilterModelConfig(
        modelConfig,
        allowedParameters
      );

      expect(result).toEqual({
        temperature: 0.7,
        top_k: 50,
        max_tokens: 1000,
      });
    });
  });

  describe('getModelSamplingParameters', () => {
    it('should return sampling parameters from system model', async () => {
      const systemModelId = 'test-model-id';
      const mockSystemModel = {
        modelRegistry: {
          samplingParameters: [SamplingParameterEnum.TEMPERATURE, SamplingParameterEnum.TOP_P],
        },
      };

      mockSystemModelsRepository.findById.mockResolvedValue(mockSystemModel);

      const result = await ModelConfigValidatorHelper.getModelSamplingParameters(
        systemModelId,
        mockSystemModelsRepository
      );

      expect(result).toEqual([SamplingParameterEnum.TEMPERATURE, SamplingParameterEnum.TOP_P]);
      expect(mockSystemModelsRepository.findById).toHaveBeenCalledWith(systemModelId);
    });

    it('should return default parameters when model registry is not available', async () => {
      const systemModelId = 'test-model-id';
      const mockSystemModel = {
        modelRegistry: null,
      };

      mockSystemModelsRepository.findById.mockResolvedValue(mockSystemModel);

      const result = await ModelConfigValidatorHelper.getModelSamplingParameters(
        systemModelId,
        mockSystemModelsRepository
      );

      expect(result).toEqual([SamplingParameterEnum.TEMPERATURE]);
    });

    it('should throw error when system model not found', async () => {
      const systemModelId = 'non-existent-model';

      mockSystemModelsRepository.findById.mockResolvedValue(null);

      await expect(
        ModelConfigValidatorHelper.getModelSamplingParameters(
          systemModelId,
          mockSystemModelsRepository
        )
      ).rejects.toThrow(AppException);
    });

    it('should handle repository errors', async () => {
      const systemModelId = 'test-model-id';

      mockSystemModelsRepository.findById.mockRejectedValue(new Error('Database error'));

      await expect(
        ModelConfigValidatorHelper.getModelSamplingParameters(
          systemModelId,
          mockSystemModelsRepository
        )
      ).rejects.toThrow(AppException);
    });
  });
});
