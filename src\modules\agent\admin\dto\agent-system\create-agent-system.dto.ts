import { ModelConfigDto } from '@modules/agent/admin/dto/common';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsString,
  IsUUID,
  Matches,
  MaxLength,
  ValidateNested,
  ValidateIf
} from 'class-validator';

/**
 * DTO cho việc tạo agent system mới
 */
export class CreateAgentSystemDto {
  /**
   * Tên hiển thị của agent
   */
  @ApiProperty({
    description: 'Tên hiển thị của agent',
    example: 'System Assistant',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  /**
   * Mã định danh của agent system, dùng để định danh trong code
   */
  @ApiProperty({
    description: 'Mã định danh của agent system, dùng để định danh trong code',
    example: 'system_assistant',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  @Matches(/^[a-z0-9_]+$/, {
    message: 'nameCode chỉ được chứa chữ cái thường, số và dấu gạch dưới',
  })
  nameCode: string;

  /**
   * MIME type của avatar
   */
  @ApiPropertyOptional({
    description: 'MIME type của avatar',
    example: 'image/jpeg',
  })
  @IsString()
  @IsOptional()
  avatarMimeType?: string;

  /**
   * Cấu hình model AI
   */
  @ApiProperty({
    description: 'Cấu hình model AI',
    type: ModelConfigDto,
  })
  @ValidateNested()
  @Type(() => ModelConfigDto)
  @IsObject()
  modelConfig: ModelConfigDto;

  /**
   * Hướng dẫn hoặc system prompt cho agent
   */
  @ApiPropertyOptional({
    description: 'Hướng dẫn hoặc system prompt cho agent',
    example:
      'Bạn là trợ lý hệ thống, hãy giúp người dùng giải đáp các thắc mắc',
  })
  @IsString()
  @IsOptional()
  instruction: string | null;

  /**
   * Mô tả về agent system (bắt buộc nếu isSupervisor = true)
   */
  @ApiPropertyOptional({
    description: 'Mô tả về agent system (bắt buộc nếu isSupervisor = true)',
    example: 'Mô tả về agent system supervisor',
  })
  @IsString()
  @ValidateIf((o) => o.isSupervisor === true)
  @IsNotEmpty({ message: 'Description là bắt buộc khi isSupervisor = true' })
  @IsOptional()
  description?: string;

  /**
   * ID của vector store
   */
  @ApiPropertyOptional({
    description: 'ID của vector store',
    example: 'vector-store-1',
  })
  @IsString()
  @IsOptional()
  vectorStoreId?: string;

  /**
   * ID của system model được sử dụng
   */
  @ApiProperty({
    description: 'ID của system model được sử dụng',
    example: 'model-uuid-123',
  })
  @IsNotEmpty()
  @IsUUID(4, { message: 'modelId phải là UUID hợp lệ' })
  modelId: string;

  /**
   * Danh sách ID của MCP systems
   */
  @ApiPropertyOptional({
    description: 'Danh sách ID của MCP systems',
    example: ['mcp-uuid-123', 'mcp-uuid-456'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true, message: 'Mỗi mcpId phải là UUID hợp lệ' })
  mcpId?: string[];

  /**
   * Có phải là supervisor không
   */
  @ApiPropertyOptional({
    description: 'Có phải là supervisor không',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  isSupervisor?: boolean;

  /**
   * Danh sách tool IDs cho agent
   */
  @ApiPropertyOptional({
    description: 'Danh sách tool IDs cho agent',
    example: ['550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true, message: 'Mỗi toolId phải là UUID hợp lệ' })
  toolIds?: string[];

  /**
   * Danh sách model registry IDs cho agent
   */
  @ApiPropertyOptional({
    description: 'Danh sách model registry IDs cho agent',
    example: ['550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true, message: 'Mỗi modelRegistryId phải là UUID hợp lệ' })
  modelRegistryIds?: string[];
}
